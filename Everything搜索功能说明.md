# Everything搜索下载功能说明

## 功能概述

在SKU对比页面新增了"Everything搜索下载"按钮，可以使用Everything搜索引擎根据商品名称搜索本地图片文件并下载到指定目录。

## 功能特点

### 1. 智能搜索
- 使用商品名称作为搜索关键词
- 自动清理商品名称中的无关前缀（如"2D Flat "）
- 支持路径限制搜索，可指定搜索范围

### 2. 批量处理
- 支持批量处理所有缺失的SKU
- 显示实时处理进度
- 异步处理，不阻塞界面

### 3. 灵活配置
- 可自定义搜索路径
- 可选择是否启用严格路径搜索
- 可指定下载目录
- 自动生成带时间戳的默认下载目录

### 4. 文件过滤
- 只下载图片文件（jpg, jpeg, png, gif, bmp, webp）
- 过滤掉过小的文件（小于1KB）
- 检查文件是否存在

## 使用方法

### 1. 前置条件
- 确保Everything软件已安装并运行
- 启用Everything的HTTP服务器功能（默认端口8080）
- 确保要搜索的图片文件已被Everything索引

### 2. 操作步骤
1. 在SKU对比页面点击"Everything搜索下载"按钮
2. 在配置对话框中设置：
   - **搜索路径**：指定搜索范围（可选）
   - **严格路径搜索**：是否仅在指定路径中搜索
   - **下载目录**：图片保存位置
3. 点击"开始下载"按钮
4. 等待批量处理完成

### 3. 配置说明

#### 搜索路径
- 留空：在整个索引中搜索
- 指定路径：限制搜索范围，提高搜索精度

#### 严格路径搜索
- 启用：使用Everything的`path:"路径"`语法严格限制搜索范围
- 禁用：在指定路径中搜索，但不严格限制

#### 下载目录
- 可手动指定或浏览选择
- 默认使用带时间戳的目录名
- 如果目录不存在会自动创建

## 技术实现

### 1. Everything API调用
```python
# 搜索参数
search_params = {
    "search": search_query,
    "json": 1,
    "path_column": 1,
    "size_column": 1,
    "sort": "name",
    "ascending": 1
}

# API地址
search_url = "http://localhost:8080/"
```

### 2. 路径限制搜索
```python
# 严格路径搜索
if search_path and strict_search:
    search_query = f'path:"{search_path}" {search_query}'
```

### 3. 图片下载
```python
# 构建Everything HTTP服务器的图片访问URL
image_url = f"http://127.0.0.1:8080/{quote(file_path)}"

# 下载图片
response = requests.get(image_url, headers=headers, stream=True, timeout=15)
```

## 注意事项

### 1. Everything配置
- 确保Everything的HTTP服务器已启用
- 默认端口为8080，如有修改需相应调整代码
- 确保防火墙允许本地HTTP访问

### 2. 文件命名
- 下载的文件以SKU命名
- 保持原始文件扩展名
- 如果文件已存在会被覆盖

### 3. 性能考虑
- 批量处理时会逐个处理SKU
- 网络请求有超时设置（15秒）
- 使用异步处理避免界面卡顿

### 4. 错误处理
- 网络请求失败会记录错误信息
- 文件不存在或无法访问会跳过
- 处理完成后显示成功/失败统计

## 故障排除

### 1. 无法连接Everything
- 检查Everything是否运行
- 检查HTTP服务器是否启用
- 检查端口是否正确（默认8080）

### 2. 搜索无结果
- 检查商品名称是否正确
- 检查文件是否已被Everything索引
- 尝试调整搜索路径设置

### 3. 下载失败
- 检查目标目录是否有写入权限
- 检查磁盘空间是否充足
- 检查文件是否被其他程序占用

## 扩展功能

### 1. 支持的搜索语法
- 基本关键词搜索
- 路径限制：`path:"路径"`
- 文件类型：`ext:jpg`
- 大小限制：`size:>1kb`

### 2. 可扩展的配置项
- Everything服务器地址和端口
- 支持的图片格式
- 文件大小限制
- 下载超时设置

### 3. 未来改进方向
- 支持多选文件下载
- 图片预览功能
- 下载进度显示
- 重复文件处理策略
