import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog, ttk
import requests
import os
import re
import json
import configparser
from datetime import datetime, timedelta
from urllib.parse import quote
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
import pyperclip
import threading
import logging
import io
from PIL import Image, ImageTk

# 导入增强版SKU对比模块
from sku_comparison_enhanced import show_enhanced_sku_comparison

# 设置日志
# 创建自定义处理器，确保使用UTF-8编码
console_handler = logging.StreamHandler()
file_handler = logging.FileHandler("product_viewer.log", mode='a', encoding='utf-8')

# 设置格式
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# 获取logger并设置处理器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# 防止日志记录重复
logger.propagate = False

# UI样式配置
class StyleConfig:
    """界面样式配置类"""
    # 字体设置
    FONT_NAME = "Microsoft YaHei"  # 微软雅黑
    FONT_SETTINGS = {
        'title': (FONT_NAME, 16, "bold"),
        'subtitle': (FONT_NAME, 10),
        'normal': (FONT_NAME, 10),
        'button': (FONT_NAME, 10, "bold"),
        'console': (FONT_NAME, 9)
    }
    
    # 颜色方案
    COLOR_SCHEME = {
        'primary_bg': '#FFFFFF',  # 主背景色 (白色)
        'secondary_bg': '#F0F0F0',  # 次要背景色 (浅灰色)
        'primary_text': '#333333',  # 主文本色 (深灰色)
        'secondary_text': '#666666',  # 次要文本色 (中灰色)
        'accent_blue': '#1E88E5',  # 强调色 - 蓝色
        'accent_green': '#43A047',  # 强调色 - 绿色
        'accent_orange': '#FB8C00',  # 强调色 - 橙色
        'accent_red': '#E53935',  # 强调色 - 红色
        'border': '#BBBBBB'  # 边框颜色 (灰色)
    }

# 配置文件处理类
class ConfigManager:
    """
    配置文件管理类
    
    处理程序配置文件的加载、保存和更新操作
    默认配置文件名为 settings.ini
    """
    CONFIG_FILE = "settings.ini"
    
    @classmethod
    def load_config(cls):
        """
        加载配置文件
        
        如果配置文件不存在，则使用默认配置并创建文件
        如果配置文件存在但缺少某些配置项，则添加默认配置项
        
        Returns:
            configparser.ConfigParser: 配置对象
        """
        config = configparser.ConfigParser(interpolation=None)  # 禁用插值功能
        
        # 默认配置
        default_settings = {
            'API': {
                'url': 'https://www.dianxiaomi.com/package/list.htm?pageNo=1&pageSize=300&shopId=6833815&state=paid&authId=-1&country=&platform=&isSearch=0&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1',
                'cookie': '',
                'base_url': 'https://www.dianxiaomi.com',
                'sku_search_url': 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
                'referer': 'https://www.dianxiaomi.com/'
            },
            'HEADERS': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'accept_encoding': 'gzip, deflate, br',
                'accept_language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'connection': 'keep-alive'
            },
            'SHARED': {
                'folder': ''
            }
        }
        
        # 检查配置文件是否存在
        if os.path.exists(cls.CONFIG_FILE):
            try:
                config.read(cls.CONFIG_FILE, encoding='utf-8')
                logger.info(f"已读取配置文件: {cls.CONFIG_FILE}")
            except Exception as e:
                logger.error(f"读取配置文件错误: {e}")
        
        # 确保所有默认配置都存在
        for section, options in default_settings.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                if not config.has_option(section, key):
                    config[section][key] = value
        
        # 保存更新后的配置
        cls.save_config(config)
        return config
    
    @classmethod
    def save_config(cls, config):
        """
        保存配置到文件
        
        Args:
            config (configparser.ConfigParser): 要保存的配置对象
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)
            logger.info(f"已保存配置到文件: {cls.CONFIG_FILE}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件错误: {e}")
            return False
            
    @classmethod
    def update_config(cls, section, key, value):
        """
        更新单个配置项
        
        Args:
            section (str): 配置区域
            key (str): 配置键
            value (str): 配置值
            
        Returns:
            bool: 是否更新成功
        """
        config = cls.load_config()
        if not config.has_section(section):
            config.add_section(section)
        config[section][key] = value
        logger.info(f"已更新配置: [{section}] {key} = {value}")
        return cls.save_config(config)

def validate_key():
    """
    验证 key.vdf 文件的有效性
    
    检查授权文件是否存在并验证其有效期
    如果验证失败则退出程序
    """
    key_file = "key.vdf"
    if not os.path.exists(key_file):
        messagebox.showerror("错误", f"未找到授权文件 {key_file}")
        exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = b'my_super_secret_password'
    salt = b'fixed_salt_value'
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    with open(key_file, "rb") as f:
        data = f.read()
        iv = data[:16]  # 前16字节为IV
        ciphertext = data[16:]  # 剩余部分为密文

    # 解密数据
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    decryptor = cipher.decryptor()
    try:
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()
    except ValueError:
        messagebox.showerror("错误", "解密失败：数据可能被篡改")
        exit(1)

    # 移除填充
    unpadder = padding.PKCS7(128).unpadder()
    try:
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()
    except ValueError:
        messagebox.showerror("错误", "数据校验失败：填充错误")
        exit(1)

    # 验证时间有效性
    try:
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))
    except ValueError:
        messagebox.showerror("错误", "时间格式无效")
        exit(1)

    # 计算时间差
    time_difference = datetime.now() - stored_time

    # 有效期验证（30天）
    if time_difference > timedelta(days=30):
        messagebox.showerror("错误", "软件授权已过期")
        exit(1)
    else:
        logger.info("验证通过，欢迎使用本软件")

class SkuExtractor:
    """
    SKU提取功能模块
    
    提供从HTML内容中提取SKU编号和商品名称的功能
    """
    
    @staticmethod
    def extract_skus(html_content):
        """
        从HTML内容中提取SKU编号
        
        Args:
            html_content (str): 需要解析的HTML内容
            
        Returns:
            list: 提取出的不重复SKU列表
        """
        # 正则表达式模式，用于匹配HTML中的SKU标识
        pattern = r'<a class="pairProInfoSku productUrl"[^>]*>([A-Za-z0-9]+)</a>'
        
        # 查找所有匹配
        matches = re.findall(pattern, html_content)
        
        # 返回去重后的结果
        return list(set(matches))
    
    @staticmethod
    def extract_product_name(html_content):
        """
        从HTML内容中提取商品名称
        
        Args:
            html_content (str): 需要解析的HTML内容
            
        Returns:
            str: 提取出的商品名称，如果未找到则返回None
        """
        # 正则表达式模式，用于匹配商品名称
        pattern = r'<span class="white-space no-new-line3 productHead">(.*?)</span>'
        
        # 查找匹配
        match = re.search(pattern, html_content)
        if match:
            return match.group(1).strip()
        return None
        
    @staticmethod
    def format_skus_for_display(skus):
        """
        将SKU列表格式化为显示文本
        
        Args:
            skus (list): SKU列表
            
        Returns:
            str: 格式化后的SKU显示文本
        """
        if not skus:
            return "未找到SKU信息"
            
        return "\n".join([
            f"找到 {len(skus)} 个SKU:",
            "─" * 30,
            *[f"{idx+1}. {sku}" for idx, sku in enumerate(skus)],
            "─" * 30
        ])

class ProductPreviewWindow:
    """
    商品预览窗口类
    
    显示商品图片和相关信息的预览窗口
    """
    def __init__(self, parent, title="商品预览", max_width=800, max_height=600):
        """
        初始化商品预览窗口
        
        Args:
            parent: 父窗口
            title (str): 窗口标题
            max_width (int): 最大窗口宽度
            max_height (int): 最大窗口高度
        """
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry(f"{max_width//2}x{max_height//2}")
        self.window.minsize(300, 200)
        self.window.configure(bg='white')
        
        # 设置为模态窗口，但允许与父窗口交互
        self.window.transient(parent)
        
        # 初始化变量
        self.max_width = max_width
        self.max_height = max_height
        self.image_pil = None
        self.image_tk = None
        
        # 创建UI组件
        self.create_widgets()
        
        # 居中窗口
        self.center_window()
        
    def create_widgets(self):
        """创建窗口组件"""
        # 主框架
        self.main_frame = tk.Frame(self.window, bg='white')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 商品信息框架
        self.info_frame = tk.Frame(self.main_frame, bg='white')
        self.info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # SKU标签
        self.sku_label = tk.Label(
            self.info_frame, 
            text="SKU: ", 
            font=("Microsoft YaHei", 10, "bold"),
            bg='white',
            anchor='w'
        )
        self.sku_label.pack(side=tk.LEFT)
        
        # 商品名称标签
        self.name_label = tk.Label(
            self.main_frame, 
            text="商品名称: ", 
            font=("Microsoft YaHei", 10),
            bg='white',
            justify='left',
            wraplength=self.max_width - 50,  # 文本换行宽度
            anchor='w'
        )
        self.name_label.pack(fill=tk.X, pady=(0, 10))
        
        # 图片状态标签
        self.status_label = tk.Label(
            self.main_frame, 
            text="正在加载图片...", 
            font=("Microsoft YaHei", 9),
            bg='white'
        )
        self.status_label.pack(fill=tk.X, pady=(0, 5))
        
        # 图片标签（用于显示图片）
        self.image_label = tk.Label(self.main_frame, bg='white')
        self.image_label.pack(fill=tk.BOTH, expand=True)
        
        # 底部按钮框架
        self.button_frame = tk.Frame(self.main_frame, bg='white')
        self.button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 关闭按钮
        self.close_button = tk.Button(
            self.button_frame, 
            text="关闭预览", 
            command=self.close,
            font=("Microsoft YaHei", 9),
            bg='#e0e0e0',
            relief=tk.FLAT,
            padx=10
        )
        self.close_button.pack(side=tk.RIGHT)
    
    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    
    def set_sku(self, sku):
        """设置SKU信息"""
        if sku:
            self.sku_label.config(text=f"SKU: {sku}")
            # 更新窗口标题
            self.window.title(f"商品预览 - {sku}")
    
    def set_product_name(self, name):
        """设置商品名称"""
        if name:
            self.name_label.config(text=f"商品名称: {name}")
    
    def load_image_from_file(self, image_path):
        """
        从本地文件加载图片
        
        Args:
            image_path (str): 图片文件路径
        """
        # 更新状态
        self.status_label.config(text="正在加载图片...")
        
        def load_and_display():
            """加载并显示图片的线程函数"""
            try:
                # 从本地文件加载图片
                self.image_pil = Image.open(image_path)
                
                # 调整图片大小，保持比例
                width, height = self.image_pil.size
                if width > self.max_width or height > self.max_height:
                    # 计算缩放比例
                    scale = min(self.max_width / width, self.max_height / height)
                    width = int(width * scale)
                    height = int(height * scale)
                    # 缩放图片
                    self.image_pil = self.image_pil.resize((width, height), Image.LANCZOS)
                
                # 转换为tkinter可显示的格式
                self.image_tk = ImageTk.PhotoImage(self.image_pil)
                
                # 在主线程中更新UI
                self.window.after(0, self._update_image)
                
            except Exception as e:
                # 捕获异常消息
                error_msg = str(e)
                # 使用闭包正确捕获错误信息
                def update_error_status(error_text):
                    return lambda: self.status_label.config(text=f"图片加载失败: {error_text}")
                
                # 更新状态为加载失败
                self.window.after(0, update_error_status(error_msg))
        
        # 创建并启动线程进行图片加载
        thread = threading.Thread(target=load_and_display)
        thread.daemon = True
        thread.start()
    
    def load_image(self, image_url):
        """
        加载并显示图片
        
        Args:
            image_url (str): 图片URL
        """
        # 更新状态
        self.status_label.config(text="正在加载图片...")
        
        def download_and_display():
            """下载并显示图片的线程函数"""
            try:
                # 发送请求获取图片数据
                response = requests.get(image_url, timeout=10)
                response.raise_for_status()
                
                # 将图片数据转换为PIL图像对象
                self.image_pil = Image.open(io.BytesIO(response.content))
                
                # 调整图片大小，保持比例
                width, height = self.image_pil.size
                if width > self.max_width or height > self.max_height:
                    # 计算缩放比例
                    scale = min(self.max_width / width, self.max_height / height)
                    width = int(width * scale)
                    height = int(height * scale)
                    # 缩放图片
                    self.image_pil = self.image_pil.resize((width, height), Image.LANCZOS)
                
                # 转换为tkinter可显示的格式
                self.image_tk = ImageTk.PhotoImage(self.image_pil)
                
                # 在主线程中更新UI
                self.window.after(0, self._update_image)
                
            except Exception as e:
                # 捕获异常消息
                error_msg = str(e)
                # 使用闭包正确捕获错误信息
                def update_error_status(error_text):
                    return lambda: self.status_label.config(text=f"图片加载失败: {error_text}")
                
                # 更新状态为加载失败
                self.window.after(0, update_error_status(error_msg))
        
        # 创建并启动线程进行图片加载
        thread = threading.Thread(target=download_and_display)
        thread.daemon = True  # 设置为守护线程，避免程序退出时线程还在运行
        thread.start()
    
    def _update_image(self):
        """更新图片显示（在主线程中调用）"""
        if self.image_tk:
            self.image_label.config(image=self.image_tk)
            self.status_label.config(text="图片加载完成")
            # 调整窗口大小以适应图片
            width = self.image_tk.width() + 40  # 添加边距
            height = self.image_tk.height() + 150  # 添加边距和其他组件的高度
            self.window.geometry(f"{width}x{height}")
            self.center_window()
    
    def close(self):
        """关闭预览窗口"""
        self.window.destroy()

class SkuComparisonWindow:
    def __init__(self, parent, api_skus, shared_skus, cookie, headers, api_sku_search_url, api_referer):
        self.parent = parent
        self.api_skus = api_skus
        self.shared_skus = shared_skus
        self.cookie = cookie
        self.headers = headers
        self.api_sku_search_url = api_sku_search_url
        self.api_referer = api_referer
        
        self.window = tk.Toplevel(parent)
        self.window.title("SKU对比结果")
        self.window.geometry("800x600")
        self.window.minsize(800, 600)
        self.window.configure(bg='white')
        
        self.temp_dir = "temp_images"
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
            
        self.loaded_images = {}
        self.preview_window = None
        self.loading_thread = None
        
        self.create_widgets()
        self.center_window()
        self.load_data()
    
    def create_widgets(self):
        self.main_frame = tk.Frame(self.window, bg='white')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        title_frame = tk.Frame(self.main_frame, bg='white')
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = tk.Label(
            title_frame,
            text="SKU对比结果",
            font=("Microsoft YaHei", 16, "bold"),
            fg="#333333",
            bg='white'
        )
        title_label.pack(side=tk.LEFT, anchor='w')
        
        missing_count = len(set(self.api_skus) - set(self.shared_skus))
        subtitle_label = tk.Label(
            title_frame,
            text=f"API总数: {len(self.api_skus)} | 文件夹总数: {len(self.shared_skus)} | 缺失总数: {missing_count}",
            font=("Microsoft YaHei", 10),
            fg="#666666",
            bg='white'
        )
        subtitle_label.pack(side=tk.LEFT, anchor='w', padx=(20, 0))
        
        control_frame = tk.Frame(self.main_frame, bg='white')
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        search_label = tk.Label(
            control_frame,
            text="搜索:",
            font=("Microsoft YaHei", 10),
            fg="#333333",
            bg='white'
        )
        search_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(
            control_frame,
            font=("Microsoft YaHei", 10),
            bg='#F0F0F0',
            fg='#333333',
            width=30,
            textvariable=self.search_var
        )
        self.search_entry.pack(side=tk.LEFT)
        
        self.search_button = tk.Button(
            control_frame,
            text="搜索",
            font=("Microsoft YaHei", 10),
            bg='#1E88E5',
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.search_sku
        )
        self.search_button.pack(side=tk.LEFT, padx=(5, 0))
        
        self.search_var.trace_add("write", self.on_search_change)
        
        list_frame = tk.Frame(self.main_frame, bg='white', relief=tk.GROOVE, bd=1)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview控件，包含图片、SKU、名称和状态四列
        self.tree = ttk.Treeview(
            list_frame, 
            columns=("image", "sku", "name", "status"),
            show="headings",
            selectmode="browse"
        )
        
        self.tree.heading("image", text="图片")
        self.tree.heading("sku", text="SKU")
        self.tree.heading("name", text="商品名称")
        self.tree.heading("status", text="状态")
        
        # 设置列宽
        self.tree.column("image", width=60, anchor="center")
        self.tree.column("sku", width=150)
        self.tree.column("name", width=450)
        self.tree.column("status", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.tree.bind("<Motion>", self.on_mouse_move)
        self.tree.bind("<Leave>", self.on_mouse_leave)
        self.tree.bind("<Button-3>", self.on_right_click)
        
        # 状态栏
        status_frame = tk.Frame(self.main_frame, bg='white')
        status_frame.pack(fill=tk.X)
        
        self.status_label = tk.Label(
            status_frame,
            text="就绪",
            font=("Microsoft YaHei", 9),
            fg="#666666",
            bg='white',
            anchor='w'
        )
        self.status_label.pack(fill=tk.X)
        
        # 设置图片列区域
        self.image_column_width = 60
    
    def center_window(self):
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    
    def load_data(self):
        # 获取缺失的SKU
        missing_skus = set(self.api_skus) - set(self.shared_skus)
        
        # 清空当前显示
        self.tree.delete(*self.tree.get_children())
        
        # 添加缺失的SKU
        for sku in sorted(missing_skus):
            self.tree.insert("", tk.END, values=("", sku, "加载中...", "缺失"), tags=(sku,))
        
        # 开始加载SKU详情
        if self.loading_thread is not None and self.loading_thread.is_alive():
            return
            
        self.loading_thread = threading.Thread(target=self.load_sku_details, daemon=True)
        self.loading_thread.start()
    
    def load_sku_details(self):
        # 获取所有待加载的SKU项
        items = self.tree.get_children()
        total_items = len(items)
        
        # 逐个处理SKU
        for i, item in enumerate(items):
            try:
                current_values = self.tree.item(item)["values"]
                if len(current_values) < 2:
                    continue
                    
                sku = current_values[1]  # SKU在第二列
                
                # 更新状态
                self.status_label.config(text=f"正在加载第 {i+1}/{total_items} 个SKU详情: {sku}")
                self.window.update_idletasks()
                
                # 获取SKU的商品信息和图片
                product_name, image_url = self.get_sku_details(sku)
                
                if product_name:
                    # 更新商品名称
                    self.tree.item(item, values=("", sku, product_name, "缺失"))
                    
                    # 如果有图片，异步加载图片
                    if image_url:
                        thread = threading.Thread(
                            target=lambda s=sku, u=image_url, i=item: self.load_and_cache_image(s, u, i), 
                            daemon=True
                        )
                        thread.start()
                else:
                    self.tree.item(item, values=("", sku, "未找到商品信息", "缺失"))
            except Exception as e:
                try:
                    # 异常处理
                    current_values = self.tree.item(item)["values"]
                    if len(current_values) >= 2:
                        sku = current_values[1]
                        self.tree.item(item, values=("", sku, f"加载失败: {str(e)[:30]}", "缺失"))
                except:
                    pass
        
        # 更新加载完成状态
        self.status_label.config(text=f"所有SKU信息加载完成，共 {total_items} 个")
    
    def get_sku_details(self, sku):
        # 设置请求头
        headers = {
            'User-Agent': self.headers['User-Agent'],
            'Cookie': self.cookie,
            'Referer': self.api_referer,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # 设置请求参数
        payload = {
            'sortName': '2',
            'pageNo': '1',
            'pageSize': '50',
            'searchType': '2',
            'searchValue': sku,
            'productSearchType': '1',
            'shopId': '-1',
            'dxmState': 'online',
            'site': '0',
            'fullCid': '',
            'sortValue': '2',
            'productType': '',
            'productStatus': 'ONLINE'
        }
        
        # 发送请求获取SKU信息
        response = requests.post(self.api_sku_search_url, headers=headers, data=payload, timeout=30)
        response.raise_for_status()
        
        # 解析JSON响应
        json_data = response.json()
        
        # 检查响应是否成功
        if json_data.get('code') != 0 or 'data' not in json_data:
            return None, None
            
        # 获取商品列表
        product_list = json_data.get('data', {}).get('page', {}).get('list', [])
        if not product_list:
            return None, None
            
        # 获取第一个商品信息
        product_info = product_list[0]
        product_name = product_info.get('productName', '')
        
        # 获取商品图片URL
        thumb_url = None
        variations = product_info.get('variations', [])
        if variations and variations[0].get('thumbUrl'):
            thumb_url = variations[0].get('thumbUrl')
            
        return product_name, thumb_url
    
    def load_and_cache_image(self, sku, image_url, item_id):
        """加载和缓存图片"""
        try:
            # 确定本地存储路径
            local_path = os.path.join(self.temp_dir, f"{sku}.png")
            
            # 如果图片不存在，下载图片
            if not os.path.exists(local_path):
                # 设置请求头
                api_headers = {
                    'User-Agent': self.headers['User-Agent'],
                    'Referer': self.api_referer,
                    'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
                }
                
                # 下载图片
                img_response = requests.get(image_url, headers=api_headers, timeout=15)
                img_response.raise_for_status()
                
                # 保存图片到本地
                with open(local_path, 'wb') as f:
                    f.write(img_response.content)
            
            # 打开和处理图片
            try:
                img = Image.open(local_path)
                
                # 创建两种尺寸的图片
                small_img = img.resize((50, 50), Image.LANCZOS)
                preview_img = img.resize((300, 300), Image.LANCZOS)
                
                # 存储到缓存中
                self.loaded_images[sku] = {
                    'small': ImageTk.PhotoImage(small_img),
                    'preview': ImageTk.PhotoImage(preview_img),
                    'path': local_path
                }
                
                # 在主线程中更新UI
                self.window.after(0, lambda: self.update_tree_image(item_id, sku))
                
            except Exception as e:
                print(f"处理图片失败 {sku}: {str(e)}")
                
        except Exception as e:
            print(f"加载图片失败 {sku}: {str(e)}")
    
    def update_tree_image(self, item_id, sku):
        """更新树形控件中的图片"""
        try:
            if item_id and sku in self.loaded_images:
                # 获取当前值
                current_values = self.tree.item(item_id)["values"]
                if len(current_values) >= 4:
                    # 更新显示，将第一列设为"图"，同时添加图片
                    self.tree.item(item_id, values=("图", current_values[1], current_values[2], current_values[3]), 
                                 image=self.loaded_images[sku]['small'])
        except Exception as e:
            print(f"更新树视图图片失败: {str(e)}")
    
    def on_mouse_move(self, event):
        """处理鼠标移动事件"""
        # 获取鼠标所在的行和列
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        
        # 如果不在图片列上，隐藏预览窗口
        if not item or not column == "#1":  # 仅当鼠标在第一列（图片列）时显示预览
            if self.preview_window:
                self.preview_window.destroy()
                self.preview_window = None
            return
            
        # 获取行的数据
        values = self.tree.item(item)["values"]
        if not values or len(values) < 2:
            return
            
        # 获取SKU
        sku = values[1]  # SKU在第二列
        
        # 如果该SKU没有加载图片，则不显示预览
        if sku not in self.loaded_images:
            return
            
        # 获取鼠标在屏幕上的坐标
        x, y = self.window.winfo_pointerxy()
        
        # 如果已经有预览窗口，则更新其位置
        if self.preview_window:
            try:
                self.preview_window.geometry(f"300x300+{x+20}+{y}")
            except:
                # 如果更新失败，关闭并重新创建
                self.preview_window.destroy()
                self.preview_window = None
        else:
            # 创建新的预览窗口
            self.preview_window = tk.Toplevel(self.window)
            self.preview_window.overrideredirect(True)  # 无边框窗口
            self.preview_window.geometry(f"300x300+{x+20}+{y}")
            self.preview_window.attributes('-topmost', True)  # 置顶显示
            
            # 添加预览图片
            label = tk.Label(self.preview_window, image=self.loaded_images[sku]['preview'])
            label.pack(fill=tk.BOTH, expand=True)
            
            # 添加边框
            self.preview_window.configure(bg='black')
            frame = tk.Frame(self.preview_window, bd=1, relief=tk.SOLID)
            frame.place(relx=0, rely=0, relwidth=1, relheight=1)
            label.place(in_=frame, relx=0, rely=0, relwidth=1, relheight=1)
    
    def on_mouse_leave(self, event):
        """处理鼠标离开事件"""
        # 关闭预览窗口
        if self.preview_window:
            self.preview_window.destroy()
            self.preview_window = None
    
    def on_right_click(self, event):
        """处理右键点击事件"""
        # 获取点击的行
        item = self.tree.identify_row(event.y)
        if not item:
            return
            
        # 获取行数据
        values = self.tree.item(item)["values"]
        if not values or len(values) < 3:
            return
            
        # 提取SKU和商品名称
        sku = values[1]  # SKU在第二列
        name = values[2]  # 名称在第三列
        
        # 创建右键菜单
        menu = tk.Menu(self.window, tearoff=0)
        menu.add_command(label=f"复制SKU: {sku}", command=lambda: self.copy_to_clipboard(sku))
        
        # 只有当商品名称存在且有效时才添加复制商品名的选项
        if name and name != "加载中..." and not name.startswith("加载失败") and not name.startswith("未找到"):
            # 如果名称太长，显示截断版本
            menu_label = f"复制商品名: {name[:20]}..." if len(name) > 20 else f"复制商品名: {name}"
            menu.add_command(label=menu_label, command=lambda: self.copy_to_clipboard(name))
            
        # 显示菜单
        menu.tk_popup(event.x_root, event.y_root)
    
    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        pyperclip.copy(text)
        self.status_label.config(text=f"已复制: {text[:30]}...")
    
    def search_sku(self):
        """搜索SKU或商品名"""
        # 获取搜索文本
        search_text = self.search_var.get().lower().strip()
        
        # 如果搜索文本为空，清除所有高亮
        if not search_text:
            for item in self.tree.get_children():
                self.tree.item(item, tags=())
            return
            
        # 遍历每一行，查找匹配项
        for item in self.tree.get_children():
            values = self.tree.item(item)["values"]
            
            # 确保有有效值
            if len(values) >= 3:
                sku = values[1].lower()  # SKU在第二列
                name = values[2].lower()  # 名称在第三列
                
                # 如果在SKU或名称中找到匹配，高亮显示
                if search_text in sku or search_text in name:
                    self.tree.item(item, tags=("match",))
                    self.tree.see(item)  # 滚动到匹配项
                else:
                    self.tree.item(item, tags=())
                    
        # 设置匹配项的高亮样式
        self.tree.tag_configure("match", background="#FFFF99")
    
    def on_search_change(self, *args):
        """搜索文本变化时自动搜索"""
        self.search_sku()

class ProductApp:
    """
    商品数据提取工具的主应用类
    
    使用tkinter构建GUI界面
    负责创建界面、处理用户交互和执行各种功能
    """
    def __init__(self, root):
        # 设置主窗口
        self.root = root
        self.root.title("商品数据提取工具 - 精简版")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        self.root.configure(bg=StyleConfig.COLOR_SCHEME['primary_bg'])
        
        # 尝试设置图标
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
            
        # 验证密钥
        validate_key()
        
        # 加载配置
        self.config = ConfigManager.load_config()
        
        # API相关配置
        self.api_url = self.config['API']['url']
        self.api_cookie = self.config['API']['cookie']
        self.api_base_url = self.config['API']['base_url']
        self.api_sku_search_url = self.config['API']['sku_search_url']
        self.api_referer = self.config['API']['referer']
        
        # 共享文件夹配置
        self.shared_folder = self.config['SHARED']['folder']
        
        # HTTP请求头配置
        self.headers = {
            'User-Agent': self.config['HEADERS']['user_agent'],
            'Accept': self.config['HEADERS']['accept'],
            'Accept-Encoding': self.config['HEADERS']['accept_encoding'],
            'Accept-Language': self.config['HEADERS']['accept_language'],
            'Connection': self.config['HEADERS']['connection']
        }
        
        # 当前输出目录
        self.current_output_dir = ""
        
        # 创建临时图片目录
        self.temp_img_dir = "temp_images"
        if not os.path.exists(self.temp_img_dir):
            os.makedirs(self.temp_img_dir)
        
        # 创建变量
        self.api_url_var = tk.StringVar()
        self.api_cookie_var = tk.StringVar()
        self.shared_folder_var = tk.StringVar()
        self.sku_search_var = tk.StringVar()
        
        # 预览窗口引用
        self.preview_window = None
        
        # 当前预览的图片路径
        self.current_preview_image = None
        
        # 创建界面布局
        self.create_widgets()
        self.setup_layout()
        
        # 设置默认值
        self.set_default_values()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        self.main_frame = tk.Frame(
            self.root,
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            padx=20, 
            pady=15
        )
        
        # 标题区域
        self.title_frame = tk.Frame(
            self.main_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.title_label = tk.Label(
            self.title_frame,
            text="商品数据提取工具",
            font=StyleConfig.FONT_SETTINGS['title'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="提取商品信息并下载图片",
            font=StyleConfig.FONT_SETTINGS['subtitle'],
            fg=StyleConfig.COLOR_SCHEME['secondary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        
        # 创建配置区域
        self.config_frame = self.create_label_frame(" 配置选项 ")
        
        # API URL配置
        self.api_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.api_url_label = tk.Label(
            self.api_frame,
            text="API地址：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.api_url_entry = tk.Entry(
            self.api_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.api_url_var
        )
        
        # Cookie配置
        self.cookie_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.cookie_label = tk.Label(
            self.cookie_frame,
            text="Cookie：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.cookie_entry = tk.Entry(
            self.cookie_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=50,
            textvariable=self.api_cookie_var
        )
        
        # 共享文件夹配置
        self.shared_folder_frame = tk.Frame(
            self.config_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.shared_folder_label = tk.Label(
            self.shared_folder_frame,
            text="共享文件夹：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.shared_folder_entry = tk.Entry(
            self.shared_folder_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=40,
            textvariable=self.shared_folder_var
        )
        self.shared_folder_btn = tk.Button(
            self.shared_folder_frame,
            text="浏览",
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['accent_blue'],
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.browse_shared_folder
        )
        
        # 商品数据输入区域
        self.product_frame = self.create_label_frame(" 商品数据输入 ")
        self.product_text = scrolledtext.ScrolledText(
            self.product_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            height=5
        )
        
        # SKU搜索区域
        self.search_frame = tk.Frame(
            self.product_frame,
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.search_label = tk.Label(
            self.search_frame,
            text="SKU搜索：",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg']
        )
        self.search_entry = tk.Entry(
            self.search_frame,
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            width=30,
            textvariable=self.sku_search_var
        )
        self.search_button = tk.Button(
            self.search_frame,
            text="搜索商品名",
            font=StyleConfig.FONT_SETTINGS['normal'],
            bg=StyleConfig.COLOR_SCHEME['accent_blue'],
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.search_sku_name
        )
        
        # 按钮区域
        self.btn_frame = self.create_label_frame(" 操作按钮 ")
        self.extract_btn = self.create_button("提取SKU", StyleConfig.COLOR_SCHEME['accent_blue'], self.extract_sku)
        self.compare_btn = self.create_button("SKU对比", StyleConfig.COLOR_SCHEME['accent_blue'], self.compare_sku)
        
        # 控制台输出区域
        self.console_frame = self.create_label_frame(" 操作日志 ")
        self.console = scrolledtext.ScrolledText(
            self.console_frame,
            font=StyleConfig.FONT_SETTINGS['console'],
            bg=StyleConfig.COLOR_SCHEME['secondary_bg'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            height=20,
            state=tk.DISABLED
        )
        
        # 状态栏
        self.status_bar = tk.Label(
            self.main_frame,
            text="就绪",
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['secondary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            anchor='w',
            padx=10
        )
    
    def setup_layout(self):
        """设置界面布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.title_frame.pack(fill=tk.X, pady=(0, 10))
        self.title_label.pack(side=tk.TOP, anchor='w')
        self.subtitle_label.pack(side=tk.TOP, anchor='w')
        
        # 配置区域
        self.config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # API配置
        self.api_frame.pack(fill=tk.X, pady=(5, 5))
        self.api_url_label.pack(side=tk.LEFT)
        self.api_url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Cookie配置
        self.cookie_frame.pack(fill=tk.X, pady=(0, 5))
        self.cookie_label.pack(side=tk.LEFT)
        self.cookie_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 共享文件夹配置
        self.shared_folder_frame.pack(fill=tk.X, pady=(0, 5))
        self.shared_folder_label.pack(side=tk.LEFT)
        self.shared_folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.shared_folder_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 商品数据输入区域
        self.product_frame.pack(fill=tk.BOTH, expand=False, pady=(0, 10))
        self.product_text.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # SKU搜索区域
        self.search_frame.pack(fill=tk.X, expand=False)
        self.search_label.pack(side=tk.LEFT, padx=(0, 5))
        self.search_entry.pack(side=tk.LEFT)
        self.search_button.pack(side=tk.LEFT, padx=(5, 0))
        
        # 按钮区域
        self.btn_frame.pack(fill=tk.X, pady=(0, 10))
        self.extract_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.compare_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 控制台输出区域
        self.console_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.console.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_bar.pack(fill=tk.X, pady=(0, 5))
    
    def create_label_frame(self, text):
        """创建统一风格的标签框架"""
        return tk.LabelFrame(
            self.main_frame,
            text=text,
            font=StyleConfig.FONT_SETTINGS['normal'],
            fg=StyleConfig.COLOR_SCHEME['primary_text'],
            bg=StyleConfig.COLOR_SCHEME['primary_bg'],
            padx=10,
            pady=10
        )
        
    def create_button(self, text, bg_color, command):
        """创建统一风格的按钮"""
        return tk.Button(
            self.btn_frame,
            text=text,
            font=StyleConfig.FONT_SETTINGS['button'],
            bg=bg_color,
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=command
        )
    
    def set_default_values(self):
        """设置默认值"""
        self.api_url_var.set(self.api_url)
        self.api_cookie_var.set(self.api_cookie)
        self.shared_folder_var.set(self.shared_folder)
        
        # 添加变量追踪，实时保存配置
        self.api_url_var.trace_add("write", lambda *args: ConfigManager.update_config('API', 'url', self.api_url_var.get()))
        self.api_cookie_var.trace_add("write", lambda *args: ConfigManager.update_config('API', 'cookie', self.api_cookie_var.get()))
        self.shared_folder_var.trace_add("write", lambda *args: ConfigManager.update_config('SHARED', 'folder', self.shared_folder_var.get()))
    
    def browse_shared_folder(self):
        """浏览并选择共享文件夹"""
        folder_path = filedialog.askdirectory(title="选择共享文件夹", initialdir=self.shared_folder_var.get() or os.getcwd())
        if folder_path:
            self.shared_folder_var.set(folder_path)
            # 更新配置
            ConfigManager.update_config('SHARED', 'folder', folder_path)
            self.log(f"已设置共享文件夹: {folder_path}", 'info')
    
    def close_preview_window(self):
        """关闭当前预览窗口"""
        if self.preview_window is not None:
            try:
                self.preview_window.close()
            except:
                pass  # 忽略可能的错误
            self.preview_window = None
    
    def log(self, message, message_type='info'):
        """日志输出到控制台"""
        # 日志类型对应的颜色
        color_map = {
            'info': StyleConfig.COLOR_SCHEME['primary_text'],  # 黑色
            'success': StyleConfig.COLOR_SCHEME['accent_green'],  # 绿色
            'warning': StyleConfig.COLOR_SCHEME['accent_orange'],  # 橙色
            'error': StyleConfig.COLOR_SCHEME['accent_red']  # 红色
        }
        
        # 替换emoji字符，避免编码问题
        message_clean = message
        emoji_map = {
            '🔍': '[搜索]', 
            '✅': '[成功]',
            '⚠️': '[警告]',
            '❌': '[错误]',
            '🖼️': '[图片]',
            '📋': '[复制]',
            '📊': '[预览]'
        }
        for emoji, replacement in emoji_map.items():
            if emoji in message_clean:
                message_clean = message_clean.replace(emoji, replacement)
        
        # 记录到日志文件
        if message_type == 'info':
            logger.info(message_clean)
        elif message_type == 'success':
            logger.info(message_clean)  # 日志库没有success级别，使用info
        elif message_type == 'warning':
            logger.warning(message_clean)
        elif message_type == 'error':
            logger.error(message_clean)
            
        # 添加到界面控制台
        try:
            self.console.config(state=tk.NORMAL)
            self.console.insert(tk.END, message + '\n')
            
            # 获取最后一行的起始位置
            last_line_start = self.console.index(f"{int(float(self.console.index('end')))-2}.0")
            last_line_end = self.console.index(f"{int(float(self.console.index('end')))-2}.end")
            
            # 为整行设置标签
            self.console.tag_add(message_type, last_line_start, last_line_end)
            self.console.tag_config(message_type, foreground=color_map.get(message_type, 'black'))
            
            self.console.config(state=tk.DISABLED)
            self.console.see(tk.END)  # 滚动到底部
        except Exception as e:
            # 如果出现任何错误，使用简单方式添加文本
            logger.warning(f"设置日志颜色失败: {str(e)}")
            try:
                self.console.config(state=tk.NORMAL)
                self.console.insert(tk.END, message + '\n')
                self.console.config(state=tk.DISABLED)
                self.console.see(tk.END)
            except:
                # 如果连简单添加文本也失败了，忽略错误
                pass
        
        # 更新UI
        self.root.update_idletasks()
        
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
        # 更新UI
        self.root.update_idletasks()
        
    def get_product_data(self):
        """获取商品数据"""
        data_text = self.product_text.get('1.0', tk.END).strip()
        if not data_text:
            messagebox.showwarning("警告", "请输入商品数据")
            return []
            
        products = []
        for line in data_text.split('\n'):
            line = line.strip()
            if line:
                # 检查是否有分隔符"----"
                if '----' in line:
                    name, product_id = line.split('----', 1)
                    products.append((name, product_id))
                else:
                    # 没有分隔符时，直接将整行作为SKU/ID
                    products.append((line, line))
                
        return products

    def extract_sku(self):
        """从API提取SKU信息"""
        try:
            # 获取当前配置
            api_url = self.api_url_var.get().strip()
            cookie = self.api_cookie_var.get().strip()
            
            # 保存到配置文件
            ConfigManager.update_config('API', 'url', api_url)
            ConfigManager.update_config('API', 'cookie', cookie)
            self.api_url = api_url
            self.api_cookie = cookie
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            self.log("\n" + "═" * 60 + "\n开始从API提取SKU编号...", 'info')
            self.update_status("正在请求数据...")
            
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer
            }
            
            # 发送请求
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 提取SKU
            self.log("数据获取成功，正在提取SKU...", 'info')
            skus = SkuExtractor.extract_skus(response.text)
            
            # 格式化并显示结果
            result_text = SkuExtractor.format_skus_for_display(skus)
            self.log(result_text, 'success')
            
            # 结果写入输入框 - 简化为只显示SKU值
            if skus:
                self.product_text.delete('1.0', tk.END)  # 清空文本区域
                for sku in skus:
                    # 修改为每行只添加SKU，不再使用"----"分隔符
                    self.product_text.insert(tk.END, f"{sku}\n")
                self.log(f"已将 {len(skus)} 个SKU自动填入数据输入区", 'success')
            
            self.update_status("SKU提取完成")
            
        except requests.RequestException as e:
            self.log(f"请求失败: {str(e)}", 'error')
            messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            self.log(f"SKU提取过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU提取过程出错: {str(e)}")
            
    def search_sku_name(self):
        """根据SKU搜索商品名称并显示预览图片"""
        try:
            # 获取SKU输入
            sku = self.sku_search_var.get().strip()
            if not sku:
                messagebox.showwarning("警告", "请输入SKU")
                return
                
            # 获取当前配置
            cookie = self.api_cookie_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
            
            # 关闭当前预览窗口（如果有）
            self.close_preview_window()
                
            self.log(f"\n" + "═" * 60 + f"\n🔍 开始搜索SKU: {sku} 的商品名称和图片...", 'info')
            self.update_status(f"正在搜索SKU: {sku}")
            
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置正确的请求地址和参数
            api_url = self.api_sku_search_url
            
            # 设置请求参数
            payload = {
                'sortName': '2',
                'pageNo': '1',
                'pageSize': '50',
                'searchType': '2',
                'searchValue': sku,
                'productSearchType': '1',
                'shopId': '-1',
                'dxmState': 'online',
                'site': '0',
                'fullCid': '',
                'sortValue': '2',
                'productType': '',
                'productStatus': 'ONLINE'
            }
            
            # 发送请求获取商品信息
            response = requests.post(api_url, headers=headers, data=payload, timeout=30)
            response.raise_for_status()
            
            # 解析JSON响应
            json_data = response.json()
            
            # 检查是否成功
            if json_data.get('code') != 0 or 'data' not in json_data:
                error_msg = json_data.get('msg', '未知错误')
                self.log(f"❌ 查询失败: {error_msg}", 'error')
                messagebox.showerror("错误", f"查询失败: {error_msg}")
                return
                
            # 检查是否有商品数据
            product_list = json_data.get('data', {}).get('page', {}).get('list', [])
            if not product_list:
                self.log(f"⚠️ 未找到SKU {sku} 的商品信息", 'warning')
                messagebox.showinfo("提示", f"未找到SKU {sku} 的商品信息")
                return
                
            # 获取第一个商品信息
            product_info = product_list[0]
            product_name = product_info.get('productName', '')
            
            if not product_name:
                self.log(f"⚠️ 未找到SKU {sku} 的商品名称", 'warning')
                messagebox.showinfo("提示", f"未找到SKU {sku} 的商品名称")
                return
                
            # 显示结果
            self.log(f"✅ 找到商品: {product_name}", 'success')
            
            # 获取商品图片URL
            thumb_url = None
            variations = product_info.get('variations', [])
            if variations:
                thumb_url = variations[0].get('thumbUrl')
                if thumb_url:
                    self.log(f"🖼️ 获取到商品图片URL: {thumb_url}", 'info')
                else:
                    self.log(f"⚠️ 未找到SKU {sku} 的图片", 'warning')
            
            # 将商品名称复制到剪贴板
            pyperclip.copy(product_name)
            self.log(f"📋 已将商品名称复制到剪贴板", 'success')
            
            # 将SKU和商品名称添加到输入框
            current_text = self.product_text.get('1.0', tk.END).strip()
            if current_text:
                # 如果已有内容，添加到新行
                new_text = f"{current_text}\n{product_name}----{sku}"
            else:
                # 如果没有内容，直接添加
                new_text = f"{product_name}----{sku}"
                
            self.product_text.delete('1.0', tk.END)
            self.product_text.insert('1.0', new_text)
            
            # 更新状态
            self.update_status(f"已找到SKU {sku} 的商品名称")
            
            # 先下载图片到本地，再显示预览窗口
            if thumb_url:
                self.log(f"📊 正在下载商品图片...", 'info')
                
                # 确定保存的文件路径
                local_image_path = None
                try:
                    # 确定文件扩展名
                    ext = '.jpg'  # 默认扩展名
                    if '.png' in thumb_url.lower():
                        ext = '.png'
                    elif '.jpeg' in thumb_url.lower():
                        ext = '.jpeg'
                    
                    # 构建本地文件路径
                    local_image_path = os.path.join(self.temp_img_dir, f"{sku}{ext}")
                    
                    # 如果已有当前预览的图片，先关闭预览窗口
                    self.close_preview_window()
                    self.current_preview_image = local_image_path
                    
                    # 设置请求头
                    api_headers = {
                        'User-Agent': self.headers['User-Agent'],
                        'Referer': self.api_referer,
                        'Accept': self.headers['Accept']
                    }
                    
                    # 下载图片
                    img_response = requests.get(thumb_url, headers=api_headers, timeout=15)
                    img_response.raise_for_status()
                    
                    # 保存图片到本地
                    with open(local_image_path, 'wb') as f:
                        f.write(img_response.content)
                    
                    # 打印下载状态到控制台
                    download_status = f"图片已下载到: {local_image_path}"
                    print(f"下载成功: SKU {sku} 的图片已保存到 {local_image_path}")
                    self.log(f"✅ {download_status}", 'success')
                    
                    # 显示预览窗口
                    self.log(f"📊 正在打开商品预览窗口...", 'info')
                    self.preview_window = ProductPreviewWindow(self.root)
                    self.preview_window.set_sku(sku)
                    self.preview_window.set_product_name(product_name)
                    self.preview_window.load_image_from_file(local_image_path)
                    
                except Exception as e:
                    error_msg = f"下载图片失败: {str(e)}"
                    print(f"下载失败: {error_msg}")
                    self.log(f"❌ {error_msg}", 'error')
                    messagebox.showerror("错误", error_msg)
            else:
                messagebox.showinfo("商品信息", f"找到商品: {product_name}\n\n商品名称已复制到剪贴板并添加到输入框\n\n未找到商品图片")
            
        except requests.RequestException as e:
            self.log(f"❌ 请求失败: {str(e)}", 'error')
            messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            self.log(f"❌ 搜索过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"搜索过程出错: {str(e)}")

    def compare_sku(self):
        try:
            # 获取当前配置
            cookie = self.api_cookie_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            # 使用界面上的共享文件夹路径
            shared_folder = self.shared_folder_var.get().strip()
            if not shared_folder:
                # 如果界面上没有设置，则弹出选择对话框
                shared_folder = filedialog.askdirectory(title="请选择共享文件夹", initialdir=os.getcwd())
                if not shared_folder:
                    self.log("未选择共享文件夹，操作取消", 'warning')
                    return
                # 将选择的路径更新到界面和配置
                self.shared_folder_var.set(shared_folder)
                ConfigManager.update_config('SHARED', 'folder', shared_folder)
                
            self.log("\n" + "═" * 60 + "\n开始SKU对比...", 'info')
            self.update_status("正在处理SKU对比...")
            
            # 设置请求头
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer
            }
            
            # 发送请求获取SKU列表
            api_url = self.api_url_var.get().strip()
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 提取SKU
            self.log("数据获取成功，正在提取SKU...", 'info')
            api_skus = SkuExtractor.extract_skus(response.text)
            
            if not api_skus:
                self.log("未从API获取到SKU数据", 'warning')
                return
                
            # 检查共享文件夹中的文件
            if not os.path.exists(shared_folder):
                self.log(f"无法访问共享文件夹: {shared_folder}", 'error')
                messagebox.showerror("错误", "无法访问共享文件夹")
                return
                
            # 获取共享文件夹中的文件名（SKU）
            shared_skus = set()
            try:
                for filename in os.listdir(shared_folder):
                    # 获取文件名（不含扩展名）作为SKU
                    sku = os.path.splitext(filename)[0]
                    shared_skus.add(sku)
                    
                self.log(f"从共享文件夹中获取到 {len(shared_skus)} 个SKU", 'info')
            except Exception as e:
                self.log(f"读取共享文件夹出错: {str(e)}", 'error')
                messagebox.showerror("错误", f"读取共享文件夹出错: {str(e)}")
                return
            
            # 对比SKU
            missing_skus = set(api_skus) - shared_skus
            
            # 显示结果
            self.log(f"\n对比结果:", 'info')
            self.log(f"API中的SKU总数: {len(api_skus)}", 'info')
            self.log(f"共享文件夹中的SKU总数: {len(shared_skus)}", 'info')
            self.log(f"缺失的SKU数量: {len(missing_skus)}", 'info')
            
            # 打开新的对比窗口
            self.update_status("正在打开增强版SKU对比窗口...")
            
            # 使用增强版SKU对比窗口替代原始窗口
            show_enhanced_sku_comparison(
                self.root, 
                api_skus, 
                shared_skus, 
                cookie,
                self.headers,
                self.api_sku_search_url,
                self.api_referer
            )
            
            self.update_status("SKU对比完成")
            
        except requests.RequestException as e:
            self.log(f"请求失败: {str(e)}", 'error')
            messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            self.log(f"SKU对比过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU对比过程出错: {str(e)}")

    def download_missing_sku(self):
        """下载缺失的SKU图片"""
        try:
            # 获取当前配置
            cookie = self.api_cookie_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            # 获取输入框中的SKU列表
            skus = []
            text_content = self.product_text.get('1.0', tk.END).strip()
            for line in text_content.split('\n'):
                sku = line.strip()
                if sku:
                    skus.append(sku)
            
            if not skus:
                messagebox.showwarning("警告", "没有要下载的SKU")
                return
                
            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"缺失SKU下载_{timestamp}_共{len(skus)}个"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            
            # 创建请求会话
            session = requests.Session()
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置正确的请求地址和参数
            api_url = self.api_sku_search_url
            
            self.log("\n" + "═" * 60 + "\n开始下载缺失SKU的图片...", 'info')
            self.update_status("正在处理SKU下载...")
            
            # 准备下载目录
            base_dir = os.path.join(self.current_output_dir, "SKU图片下载")
            os.makedirs(base_dir, exist_ok=True)
            
            # 保存SKU列表
            sku_list_path = os.path.join(folder_name, "SKU列表.txt")
            with open(sku_list_path, 'w', encoding='utf-8') as f:
                for sku in skus:
                    f.write(f"{sku}\n")
            
            # 处理每个SKU
            success_count = 0
            
            # 创建和配置进度条
            progress_window = tk.Toplevel(self.root)
            progress_window.title("下载进度")
            progress_window.geometry("400x150")
            progress_window.configure(bg=StyleConfig.COLOR_SCHEME['primary_bg'])
            progress_window.transient(self.root)  # 设置为主窗口的临时窗口
            progress_window.grab_set()  # 阻止与其他窗口交互
            
            # 进度标签
            progress_label = tk.Label(
                progress_window, 
                text=f"正在下载SKU图片 (0/{len(skus)})",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            progress_label.pack(pady=(20, 10))
            
            # 进度条
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                progress_window, 
                orient="horizontal", 
                length=350, 
                mode="determinate",
                variable=progress_var,
                maximum=len(skus)
            )
            progress_bar.pack(pady=(0, 20))
            
            # 当前SKU标签
            current_sku_label = tk.Label(
                progress_window, 
                text="",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            current_sku_label.pack(pady=(0, 10))
            
            # 更新进度的函数
            def update_progress(idx, sku, status=None):
                progress_var.set(idx)
                progress_label.config(text=f"正在下载SKU图片 ({idx}/{len(skus)})")
                status_text = f"处理：SKU {sku}" + (f" - {status}" if status else "")
                current_sku_label.config(text=status_text)
                progress_window.update()
            
            # 开始处理SKU
            for idx, sku in enumerate(skus, 1):
                try:
                    update_progress(idx, sku)
                    self.update_status(f"处理中: SKU {sku} ({idx}/{len(skus)})")
                    self.log(f"\n[{idx}/{len(skus)}] 🔍 正在查询SKU: {sku}", 'info')
                    
                    # 设置请求参数
                    payload = {
                        'sortName': '2',
                        'pageNo': '1',
                        'pageSize': '50',
                        'searchType': '2',
                        'searchValue': sku,
                        'productSearchType': '1',
                        'shopId': '-1',
                        'dxmState': 'online',
                        'site': '0',
                        'fullCid': '',
                        'sortValue': '2',
                        'productType': '',
                        'productStatus': 'ONLINE'
                    }
                    
                    update_progress(idx, sku, "发送请求")
                    # 发送请求获取商品信息
                    response = session.post(api_url, headers=headers, data=payload, timeout=30)
                    response.raise_for_status()
                    
                    # 解析JSON响应
                    json_data = response.json()
                    
                    # 检查是否成功
                    if json_data.get('code') != 0 or 'data' not in json_data:
                        update_progress(idx, sku, "查询失败")
                        self.log(f"⚠️ 查询SKU {sku} 返回错误: {json_data.get('msg', '未知错误')}", 'warning')
                        continue
                    
                    # 检查是否有商品数据
                    product_list = json_data.get('data', {}).get('page', {}).get('list', [])
                    if not product_list:
                        update_progress(idx, sku, "未找到商品")
                        self.log(f"⚠️ 未找到SKU {sku} 的商品信息", 'warning')
                        continue
                    
                    # 获取第一个商品信息
                    product_info = product_list[0]
                    product_name = product_info.get('productName', '')
                    
                    if not product_name:
                        update_progress(idx, sku, "未找到名称")
                        self.log(f"⚠️ 未找到SKU {sku} 的商品名称", 'warning')
                        continue
                    
                    update_progress(idx, sku, f"找到商品: {product_name[:20]}...")
                    self.log(f"📝 找到商品: {product_name}", 'info')
                    
                    # 获取variations信息和thumbUrl
                    thumb_url = None
                    variations = product_info.get('variations', [])
                    if variations:
                        thumb_url = variations[0].get('thumbUrl')
                        if thumb_url:
                            self.log(f"🖼️ 获取到商品图片URL: {thumb_url}", 'info')
                        else:
                            update_progress(idx, sku, "无图片URL")
                            self.log(f"⚠️ 未找到SKU {sku} 的thumbUrl", 'warning')
                    else:
                        update_progress(idx, sku, "无商品变体")
                        self.log(f"⚠️ 未找到SKU {sku} 的variations信息", 'warning')
                    
                    # 如果有图片URL，下载图片
                    if thumb_url:
                        local_path = os.path.join(base_dir, f"{sku}.png")
                        try:
                            update_progress(idx, sku, "下载图片")
                            # 设置请求头
                            api_headers = {
                                'User-Agent': self.headers['User-Agent'],
                                'Referer': self.api_referer,
                                'Accept': self.headers['Accept']
                            }
                            
                            # 下载图片
                            img_response = requests.get(thumb_url, headers=api_headers, timeout=15)
                            img_response.raise_for_status()
                            
                            # 保存图片
                            with open(local_path, 'wb') as f:
                                f.write(img_response.content)
                                
                            update_progress(idx, sku, "下载成功")
                            self.log(f"✅ 成功下载SKU {sku} 的图片", 'success')
                            success_count += 1
                            
                        except Exception as e:
                            update_progress(idx, sku, f"下载失败: {str(e)[:20]}...")
                            self.log(f"❌ 下载SKU {sku} 图片失败: {str(e)}", 'error')
                    else:
                        update_progress(idx, sku, "无图片可下载")
                        self.log(f"❌ SKU {sku} 没有可下载的图片", 'error')
                    
                except Exception as e:
                    update_progress(idx, sku, f"处理出错: {str(e)[:20]}...")
                    self.log(f"❌ 处理SKU {sku} 时出错: {str(e)}", 'error')
            
            # 关闭进度窗口
            progress_window.destroy()
            
            # 汇总结果
            summary = f"\n" + "═" * 30 + f"\n总计处理: {len(skus)} 个SKU, 成功下载: {success_count} 个\n文件保存在: {os.path.abspath(base_dir)}"
            self.log(summary, 'success')
            self.update_status("SKU下载完成")
            messagebox.showinfo("提示", f"缺失SKU图片下载完成！\n成功: {success_count}/{len(skus)}")
            
        except Exception as e:
            self.log(f"SKU下载过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU下载过程出错: {str(e)}") 

    def search_sku_and_download(self):
        """SKU搜索并下载图片"""
        try:
            # 获取输入的SKU列表
            skus = []
            text_content = self.product_text.get('1.0', tk.END).strip()
            for line in text_content.split('\n'):
                sku = line.strip()
                if sku:
                    skus.append(sku)
            
            if not skus:
                messagebox.showwarning("警告", "请输入SKU数据")
                return
                
            # 获取当前配置
            cookie = self.api_cookie_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"SKU搜索_{timestamp}_共{len(skus)}个"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            
            # 创建请求会话
            session = requests.Session()
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置正确的请求地址和参数
            api_url = self.api_sku_search_url
            
            self.log("\n" + "═" * 60 + "\n开始SKU搜索并下载图片...", 'info')
            self.update_status("正在处理SKU...")
            
            # 准备下载目录
            base_dir = os.path.join(self.current_output_dir, "SKU图片下载")
            os.makedirs(base_dir, exist_ok=True)
            
            # 保存SKU列表
            sku_list_path = os.path.join(folder_name, "SKU列表.txt")
            with open(sku_list_path, 'w', encoding='utf-8') as f:
                for sku in skus:
                    f.write(f"{sku}\n")
            
            # 处理每个SKU
            success_count = 0
            
            # 创建和配置进度条
            progress_window = tk.Toplevel(self.root)
            progress_window.title("下载进度")
            progress_window.geometry("400x150")
            progress_window.configure(bg=StyleConfig.COLOR_SCHEME['primary_bg'])
            progress_window.transient(self.root)
            progress_window.grab_set()
            
            # 进度标签
            progress_label = tk.Label(
                progress_window, 
                text=f"正在下载SKU图片 (0/{len(skus)})",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            progress_label.pack(pady=(20, 10))
            
            # 进度条
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                progress_window, 
                orient="horizontal", 
                length=350, 
                mode="determinate",
                variable=progress_var,
                maximum=len(skus)
            )
            progress_bar.pack(pady=(0, 20))
            
            # 当前SKU标签
            current_sku_label = tk.Label(
                progress_window, 
                text="",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            current_sku_label.pack(pady=(0, 10))
            
            # 更新进度的函数
            def update_progress(idx, sku, status=None):
                progress_var.set(idx)
                progress_label.config(text=f"正在下载SKU图片 ({idx}/{len(skus)})")
                status_text = f"处理：SKU {sku}" + (f" - {status}" if status else "")
                current_sku_label.config(text=status_text)
                progress_window.update()
            
            # 开始处理SKU
            for idx, sku in enumerate(skus, 1):
                try:
                    update_progress(idx, sku)
                    self.update_status(f"处理中: SKU {sku} ({idx}/{len(skus)})")
                    self.log(f"\n[{idx}/{len(skus)}] 🔍 正在查询SKU: {sku}", 'info')
                    
                    # 设置请求参数
                    payload = {
                        'sortName': '2',
                        'pageNo': '1',
                        'pageSize': '50',
                        'searchType': '2',
                        'searchValue': sku,
                        'productSearchType': '1',
                        'shopId': '-1',
                        'dxmState': 'online',
                        'site': '0',
                        'fullCid': '',
                        'sortValue': '2',
                        'productType': '',
                        'productStatus': 'ONLINE'
                    }
                    
                    update_progress(idx, sku, "发送请求")
                    # 发送请求获取商品信息
                    response = session.post(api_url, headers=headers, data=payload, timeout=30)
                    response.raise_for_status()
                    
                    # 解析JSON响应
                    json_data = response.json()
                    
                    # 检查是否成功
                    if json_data.get('code') != 0 or 'data' not in json_data:
                        update_progress(idx, sku, "查询失败")
                        self.log(f"⚠️ 查询SKU {sku} 返回错误: {json_data.get('msg', '未知错误')}", 'warning')
                        continue
                    
                    # 检查是否有商品数据
                    product_list = json_data.get('data', {}).get('page', {}).get('list', [])
                    if not product_list:
                        update_progress(idx, sku, "未找到商品")
                        self.log(f"⚠️ 未找到SKU {sku} 的商品信息", 'warning')
                        continue
                    
                    # 获取第一个商品信息
                    product_info = product_list[0]
                    product_name = product_info.get('productName', '')
                    
                    if not product_name:
                        update_progress(idx, sku, "未找到名称")
                        self.log(f"⚠️ 未找到SKU {sku} 的商品名称", 'warning')
                        continue
                    
                    update_progress(idx, sku, f"找到商品: {product_name[:20]}...")
                    self.log(f"📝 找到商品: {product_name}", 'info')
                    
                    # 获取variations信息和thumbUrl
                    thumb_url = None
                    variations = product_info.get('variations', [])
                    if variations:
                        thumb_url = variations[0].get('thumbUrl')
                        if thumb_url:
                            self.log(f"🖼️ 获取到商品图片URL: {thumb_url}", 'info')
                        else:
                            update_progress(idx, sku, "无图片URL")
                            self.log(f"⚠️ 未找到SKU {sku} 的thumbUrl", 'warning')
                    else:
                        update_progress(idx, sku, "无商品变体")
                        self.log(f"⚠️ 未找到SKU {sku} 的variations信息", 'warning')
                    
                    # 如果有图片URL，下载图片
                    if thumb_url:
                        local_path = os.path.join(base_dir, f"{sku}.png")
                        try:
                            update_progress(idx, sku, "下载图片")
                            # 设置请求头
                            api_headers = {
                                'User-Agent': self.headers['User-Agent'],
                                'Referer': self.api_referer,
                                'Accept': self.headers['Accept']
                            }
                            
                            # 下载图片
                            img_response = requests.get(thumb_url, headers=api_headers, timeout=15)
                            img_response.raise_for_status()
                            
                            # 保存图片
                            with open(local_path, 'wb') as f:
                                f.write(img_response.content)
                                
                            update_progress(idx, sku, "下载成功")
                            self.log(f"✅ 成功下载SKU {sku} 的图片", 'success')
                            success_count += 1
                            
                        except Exception as e:
                            update_progress(idx, sku, f"下载失败: {str(e)[:20]}...")
                            self.log(f"❌ 下载SKU {sku} 图片失败: {str(e)}", 'error')
                    else:
                        update_progress(idx, sku, "无图片可下载")
                        self.log(f"❌ SKU {sku} 没有可下载的图片", 'error')
                    
                except Exception as e:
                    update_progress(idx, sku, f"处理出错: {str(e)[:20]}...")
                    self.log(f"❌ 处理SKU {sku} 时出错: {str(e)}", 'error')
            
            # 关闭进度窗口
            progress_window.destroy()
            
            # 汇总结果
            summary = f"\n" + "═" * 30 + f"\n总计处理: {len(skus)} 个SKU, 成功下载: {success_count} 个\n文件保存在: {os.path.abspath(base_dir)}"
            self.log(summary, 'success')
            self.update_status("SKU下载完成")
            messagebox.showinfo("提示", f"SKU图片下载完成！\n成功: {success_count}/{len(skus)}")
            
        except Exception as e:
            self.log(f"SKU下载过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"SKU下载过程出错: {str(e)}")

    def download_product_images(self):
        """下载商品图片功能"""
        try:
            # 获取商品数据
            products = self.get_product_data()
            if not products:
                messagebox.showwarning("警告", "没有有效的商品数据")
                return
                
            # 获取当前配置
            cookie = self.api_cookie_var.get().strip()
            
            # 验证必要参数
            if not cookie:
                messagebox.showwarning("警告", "请输入Cookie信息")
                return
                
            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"商品数据_{timestamp}_共{len(products)}组"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            
            # 创建商品列表文件
            txt_path = os.path.join(folder_name, "商品列表.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                for name, product_id in products:
                    f.write(f"{name}----{product_id}\n")
                    
            self.log("\n" + "═" * 60 + "\n开始下载商品图片...", 'info')
            self.update_status("正在下载商品图片...")
            
            # 准备下载目录
            base_dir = os.path.join(self.current_output_dir, "出单图下载")
            os.makedirs(base_dir, exist_ok=True)
            
            # 创建请求会话
            session = requests.Session()
            headers = {
                'User-Agent': self.headers['User-Agent'],
                'Cookie': cookie,
                'Referer': self.api_referer,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 创建和配置进度条
            progress_window = tk.Toplevel(self.root)
            progress_window.title("下载进度")
            progress_window.geometry("400x150")
            progress_window.configure(bg=StyleConfig.COLOR_SCHEME['primary_bg'])
            progress_window.transient(self.root)
            progress_window.grab_set()
            
            # 进度标签
            progress_label = tk.Label(
                progress_window, 
                text=f"正在下载商品图片 (0/{len(products)})",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            progress_label.pack(pady=(20, 10))
            
            # 进度条
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                progress_window, 
                orient="horizontal", 
                length=350, 
                mode="determinate",
                variable=progress_var,
                maximum=len(products)
            )
            progress_bar.pack(pady=(0, 20))
            
            # 当前商品标签
            current_product_label = tk.Label(
                progress_window, 
                text="",
                font=StyleConfig.FONT_SETTINGS['normal'],
                bg=StyleConfig.COLOR_SCHEME['primary_bg']
            )
            current_product_label.pack(pady=(0, 10))
            
            # 更新进度的函数
            def update_progress(idx, product_name, product_id, status=None):
                progress_var.set(idx)
                progress_label.config(text=f"正在下载商品图片 ({idx}/{len(products)})")
                display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
                status_text = f"处理：{display_name} (ID: {product_id})" + (f" - {status}" if status else "")
                current_product_label.config(text=status_text)
                progress_window.update()
                
            # 处理每个商品
            success_count = 0
            for idx, (product_name, product_id) in enumerate(products, 1):
                try:
                    update_progress(idx, product_name, product_id)
                    self.update_status(f"处理中: 商品 {product_id} ({idx}/{len(products)})")
                    self.log(f"\n[{idx}/{len(products)}] 🔍 正在处理商品: {product_name} (ID: {product_id})", 'info')
                    
                    # 尝试将product_id作为SKU搜索
                    payload = {
                        'sortName': '2',
                        'pageNo': '1',
                        'pageSize': '50',
                        'searchType': '2',
                        'searchValue': product_id,
                        'productSearchType': '1',
                        'shopId': '-1',
                        'dxmState': 'online',
                        'site': '0',
                        'fullCid': '',
                        'sortValue': '2',
                        'productType': '',
                        'productStatus': 'ONLINE'
                    }
                    
                    update_progress(idx, product_name, product_id, "发送请求")
                    # 发送请求获取商品信息
                    api_url = self.api_sku_search_url
                    response = session.post(api_url, headers=headers, data=payload, timeout=30)
                    response.raise_for_status()
                    
                    # 解析JSON响应
                    json_data = response.json()
                    
                    # 检查是否成功
                    if json_data.get('code') != 0 or 'data' not in json_data:
                        update_progress(idx, product_name, product_id, "查询失败")
                        self.log(f"⚠️ 查询商品ID {product_id} 返回错误: {json_data.get('msg', '未知错误')}", 'warning')
                        continue
                    
                    # 检查是否有商品数据
                    product_list = json_data.get('data', {}).get('page', {}).get('list', [])
                    if not product_list:
                        update_progress(idx, product_name, product_id, "未找到商品")
                        self.log(f"⚠️ 未找到商品ID {product_id} 的商品信息", 'warning')
                        continue
                    
                    # 获取第一个商品信息
                    product_info = product_list[0]
                    api_product_name = product_info.get('productName', '')
                    
                    if not api_product_name:
                        update_progress(idx, product_name, product_id, "未找到名称")
                        self.log(f"⚠️ 未找到商品ID {product_id} 的商品名称", 'warning')
                        continue
                    
                    update_progress(idx, product_name, product_id, f"找到商品: {api_product_name[:20]}...")
                    self.log(f"📝 找到商品: {api_product_name}", 'info')
                    
                    # 获取variations信息和thumbUrl
                    thumb_url = None
                    variations = product_info.get('variations', [])
                    if variations:
                        thumb_url = variations[0].get('thumbUrl')
                        if thumb_url:
                            self.log(f"🖼️ 获取到商品图片URL: {thumb_url}", 'info')
                        else:
                            update_progress(idx, product_name, product_id, "无图片URL")
                            self.log(f"⚠️ 未找到商品ID {product_id} 的thumbUrl", 'warning')
                    else:
                        update_progress(idx, product_name, product_id, "无商品变体")
                        self.log(f"⚠️ 未找到商品ID {product_id} 的variations信息", 'warning')
                    
                    # 如果有图片URL，下载图片
                    if thumb_url:
                        # 确定文件扩展名
                        ext = '.jpg'  # 默认扩展名
                        if '.png' in thumb_url.lower():
                            ext = '.png'
                        elif '.jpeg' in thumb_url.lower():
                            ext = '.jpeg'
                            
                        local_path = os.path.join(base_dir, f"{product_id}{ext}")
                        try:
                            update_progress(idx, product_name, product_id, "下载图片")
                            # 设置请求头
                            api_headers = {
                                'User-Agent': self.headers['User-Agent'],
                                'Referer': self.api_referer,
                                'Accept': self.headers['Accept']
                            }
                            
                            # 下载图片
                            img_response = requests.get(thumb_url, headers=api_headers, timeout=15)
                            img_response.raise_for_status()
                            
                            # 保存图片
                            with open(local_path, 'wb') as f:
                                f.write(img_response.content)
                                
                            update_progress(idx, product_name, product_id, "下载成功")
                            self.log(f"✅ 成功下载商品 {product_id} 的图片", 'success')
                            success_count += 1
                            
                        except Exception as e:
                            update_progress(idx, product_name, product_id, f"下载失败: {str(e)[:20]}...")
                            self.log(f"❌ 下载商品 {product_id} 图片失败: {str(e)}", 'error')
                    else:
                        update_progress(idx, product_name, product_id, "无图片可下载")
                        self.log(f"❌ 商品 {product_id} 没有可下载的图片", 'error')
                    
                except Exception as e:
                    update_progress(idx, product_name, product_id, f"处理出错: {str(e)[:20]}...")
                    self.log(f"❌ 处理商品 {product_id} 时出错: {str(e)}", 'error')
            
            # 关闭进度窗口
            progress_window.destroy()
            
            # 汇总结果
            summary = f"\n" + "═" * 30 + f"\n总计处理: {len(products)} 个商品, 成功下载: {success_count} 个\n文件保存在: {os.path.abspath(base_dir)}"
            self.log(summary, 'success')
            self.update_status("商品图片下载完成")
            messagebox.showinfo("提示", f"商品图片下载完成！\n成功: {success_count}/{len(products)}")
            
        except Exception as e:
            self.log(f"下载商品图片过程出错: {str(e)}", 'error')
            messagebox.showerror("错误", f"下载商品图片过程出错: {str(e)}") 

# 程序入口点
if __name__ == "__main__":
    try:
        # 创建主窗口
        root = tk.Tk()
        app = ProductApp(root)
        
        # 运行主循环
        root.mainloop()
    except Exception as e:
        # 记录全局异常
        logger.error(f"程序运行出错: {str(e)}")
        try:
            messagebox.showerror("错误", f"程序运行出错: {str(e)}")
        except:
            print(f"\n严重错误: {str(e)}\n请检查日志文件获取更多信息。") 