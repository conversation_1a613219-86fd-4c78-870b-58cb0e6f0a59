#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Everything搜索功能
"""

import tkinter as tk
from sku_comparison_enhanced import show_enhanced_sku_comparison

def test_everything_function():
    """测试Everything搜索功能"""
    
    # 创建主窗口
    root = tk.Tk()
    root.title("测试Everything功能")
    root.geometry("400x300")
    
    # 模拟数据
    api_skus = ["ML123456", "ML789012", "ML345678"]
    shared_skus = ["ML123456"]  # 模拟已存在的SKU
    cookie = "test_cookie"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    api_sku_search_url = "https://example.com/api/search"
    api_referer = "https://example.com"
    
    # 创建测试按钮
    test_button = tk.Button(
        root,
        text="打开增强版SKU对比窗口",
        command=lambda: show_enhanced_sku_comparison(
            root, api_skus, shared_skus, cookie, headers, api_sku_search_url, api_referer
        ),
        font=("Microsoft YaHei", 12),
        bg='#1E88E5',
        fg='white',
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    # 添加说明文本
    info_label = tk.Label(
        root,
        text="点击按钮测试Everything搜索下载功能\n\n注意：需要Everything软件运行并启用HTTP服务器",
        font=("Microsoft YaHei", 10),
        justify=tk.CENTER,
        wraplength=350
    )
    info_label.pack(expand=True, padx=20, pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    test_everything_function()
