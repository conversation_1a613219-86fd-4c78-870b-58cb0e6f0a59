#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试Everything功能
"""

import tkinter as tk
from tkinter import messagebox

def test_everything_api():
    """测试Everything API连接"""
    try:
        import requests
        
        # 测试Everything API连接
        search_url = "http://localhost:8080/"
        search_params = {
            "search": "test",
            "json": 1,
            "path_column": 1,
            "size_column": 1,
            "sort": "name",
            "ascending": 1
        }
        
        response = requests.get(search_url, params=search_params, timeout=5)
        
        if response.status_code == 200:
            messagebox.showinfo("成功", "Everything API连接成功！")
            print("Everything API响应:", response.text[:200])
        else:
            messagebox.showerror("错误", f"Everything API响应错误: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        messagebox.showerror("连接错误", "无法连接到Everything HTTP服务器\n请确保Everything正在运行并启用了HTTP服务器")
    except Exception as e:
        messagebox.showerror("错误", f"测试Everything API时出错: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    root.title("Everything API测试")
    root.geometry("300x200")
    
    # 创建测试按钮
    test_button = tk.Button(
        root,
        text="测试Everything API",
        command=test_everything_api,
        font=("Microsoft YaHei", 12),
        bg='#1E88E5',
        fg='white',
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    # 说明文本
    info_label = tk.Label(
        root,
        text="点击按钮测试Everything API连接\n\n确保Everything运行并启用HTTP服务器",
        font=("Microsoft YaHei", 9),
        justify=tk.CENTER
    )
    info_label.pack(expand=True, padx=10, pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
