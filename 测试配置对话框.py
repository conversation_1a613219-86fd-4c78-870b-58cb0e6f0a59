#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Everything配置对话框
"""

import tkinter as tk
from tkinter import messagebox, filedialog
from datetime import datetime

class EverythingConfigDialog:
    """Everything搜索配置对话框"""
    def __init__(self, parent, sku_list):
        self.parent = parent
        self.sku_list = sku_list
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Everything搜索配置")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.configure(bg='white')
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_dialog()
        
        # 创建界面
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建对话框控件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg='white', padx=20, pady=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="Everything搜索下载配置",
            font=("Microsoft YaHei", 14, "bold"),
            bg='white',
            fg='#333333'
        )
        title_label.pack(pady=(0, 15))
        
        # SKU信息
        info_frame = tk.LabelFrame(main_frame, text="下载信息", bg='white', padx=10, pady=10)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(
            info_frame,
            text=f"将要下载 {len(self.sku_list)} 个SKU的图片",
            bg='white',
            font=("Microsoft YaHei", 10)
        ).pack(anchor='w')
        
        # 搜索路径配置
        search_frame = tk.LabelFrame(main_frame, text="搜索配置", bg='white', padx=10, pady=10)
        search_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 搜索路径
        path_frame = tk.Frame(search_frame, bg='white')
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(path_frame, text="搜索路径:", bg='white', width=12, anchor='e').pack(side=tk.LEFT)
        self.search_path_var = tk.StringVar()
        self.search_path_entry = tk.Entry(
            path_frame,
            textvariable=self.search_path_var,
            font=("Microsoft YaHei", 9),
            width=35
        )
        self.search_path_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        
        tk.Button(
            path_frame,
            text="浏览",
            command=self.browse_search_path,
            font=("Microsoft YaHei", 9)
        ).pack(side=tk.RIGHT)
        
        # 严格搜索选项
        self.strict_search_var = tk.BooleanVar()
        strict_check = tk.Checkbutton(
            search_frame,
            text="启用严格路径搜索（仅在指定路径中搜索）",
            variable=self.strict_search_var,
            bg='white',
            font=("Microsoft YaHei", 9)
        )
        strict_check.pack(anchor='w')
        
        # 下载目录配置
        download_frame = tk.LabelFrame(main_frame, text="下载配置", bg='white', padx=10, pady=10)
        download_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 下载目录
        dir_frame = tk.Frame(download_frame, bg='white')
        dir_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(dir_frame, text="下载目录:", bg='white', width=12, anchor='e').pack(side=tk.LEFT)
        self.download_dir_var = tk.StringVar()
        self.download_dir_entry = tk.Entry(
            dir_frame,
            textvariable=self.download_dir_var,
            font=("Microsoft YaHei", 9),
            width=35
        )
        self.download_dir_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        
        tk.Button(
            dir_frame,
            text="浏览",
            command=self.browse_download_dir,
            font=("Microsoft YaHei", 9)
        ).pack(side=tk.RIGHT)
        
        # 设置默认下载目录
        default_dir = f"Everything下载_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.download_dir_var.set(default_dir)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        tk.Button(
            button_frame,
            text="取消",
            command=self.cancel,
            font=("Microsoft YaHei", 10),
            width=10
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        tk.Button(
            button_frame,
            text="开始下载",
            command=self.confirm,
            font=("Microsoft YaHei", 10, "bold"),
            bg='#43A047',
            fg='white',
            width=10
        ).pack(side=tk.RIGHT)
    
    def browse_search_path(self):
        """浏览搜索路径"""
        path = filedialog.askdirectory(title="选择搜索路径")
        if path:
            self.search_path_var.set(path)
    
    def browse_download_dir(self):
        """浏览下载目录"""
        path = filedialog.askdirectory(title="选择下载目录")
        if path:
            self.download_dir_var.set(path)
    
    def confirm(self):
        """确认配置"""
        search_path = self.search_path_var.get().strip()
        download_dir = self.download_dir_var.get().strip()
        strict_search = self.strict_search_var.get()
        
        if not download_dir:
            messagebox.showwarning("警告", "请指定下载目录")
            return
        
        self.result = {
            'search_path': search_path,
            'download_dir': download_dir,
            'strict_search': strict_search
        }
        
        messagebox.showinfo("配置结果", 
                           f"搜索路径: {search_path or '(全局搜索)'}\n"
                           f"下载目录: {download_dir}\n"
                           f"严格搜索: {'是' if strict_search else '否'}")
        
        self.dialog.destroy()
    
    def cancel(self):
        """取消配置"""
        self.result = None
        self.dialog.destroy()

def test_config_dialog():
    """测试配置对话框"""
    root = tk.Tk()
    root.title("测试Everything配置对话框")
    root.geometry("300x200")
    
    def show_dialog():
        test_skus = ["ML123456", "ML789012", "ML345678"]
        dialog = EverythingConfigDialog(root, test_skus)
        if dialog.result:
            print("用户配置:", dialog.result)
        else:
            print("用户取消了配置")
    
    test_button = tk.Button(
        root,
        text="打开配置对话框",
        command=show_dialog,
        font=("Microsoft YaHei", 12),
        bg='#1E88E5',
        fg='white',
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    test_config_dialog()
