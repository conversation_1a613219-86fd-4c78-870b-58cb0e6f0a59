#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证Everything配置对话框是否正常显示
"""

import tkinter as tk
from tkinter import messagebox, filedialog
from datetime import datetime

def create_config_dialog():
    """创建配置对话框"""
    
    # 创建主窗口
    root = tk.Tk()
    root.title("主窗口")
    root.geometry("300x200")
    
    def show_config():
        """显示配置对话框"""
        # 创建对话框窗口
        dialog = tk.Toplevel(root)
        dialog.title("Everything搜索配置")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.configure(bg='white')
        
        # 设置为模态对话框
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f'{width}x{height}+{x}+{y}')
        
        # 主框架
        main_frame = tk.Frame(dialog, bg='white', padx=20, pady=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="Everything搜索下载配置",
            font=("Microsoft YaHei", 14, "bold"),
            bg='white',
            fg='#333333'
        )
        title_label.pack(pady=(0, 15))
        
        # SKU信息
        info_frame = tk.LabelFrame(main_frame, text="下载信息", bg='white', padx=10, pady=10)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(
            info_frame,
            text="将要下载 3 个SKU的图片",
            bg='white',
            font=("Microsoft YaHei", 10)
        ).pack(anchor='w')
        
        # 搜索路径配置
        search_frame = tk.LabelFrame(main_frame, text="搜索配置", bg='white', padx=10, pady=10)
        search_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 搜索路径
        path_frame = tk.Frame(search_frame, bg='white')
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(path_frame, text="搜索路径:", bg='white', width=12, anchor='e').pack(side=tk.LEFT)
        search_path_var = tk.StringVar()
        search_path_entry = tk.Entry(
            path_frame,
            textvariable=search_path_var,
            font=("Microsoft YaHei", 9),
            width=35
        )
        search_path_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        
        def browse_search_path():
            path = filedialog.askdirectory(title="选择搜索路径")
            if path:
                search_path_var.set(path)
        
        tk.Button(
            path_frame,
            text="浏览",
            command=browse_search_path,
            font=("Microsoft YaHei", 9)
        ).pack(side=tk.RIGHT)
        
        # 严格搜索选项
        strict_search_var = tk.BooleanVar()
        strict_check = tk.Checkbutton(
            search_frame,
            text="启用严格路径搜索（仅在指定路径中搜索）",
            variable=strict_search_var,
            bg='white',
            font=("Microsoft YaHei", 9)
        )
        strict_check.pack(anchor='w')
        
        # 下载目录配置
        download_frame = tk.LabelFrame(main_frame, text="下载配置", bg='white', padx=10, pady=10)
        download_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 下载目录
        dir_frame = tk.Frame(download_frame, bg='white')
        dir_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(dir_frame, text="下载目录:", bg='white', width=12, anchor='e').pack(side=tk.LEFT)
        download_dir_var = tk.StringVar()
        download_dir_entry = tk.Entry(
            dir_frame,
            textvariable=download_dir_var,
            font=("Microsoft YaHei", 9),
            width=35
        )
        download_dir_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        
        def browse_download_dir():
            path = filedialog.askdirectory(title="选择下载目录")
            if path:
                download_dir_var.set(path)
        
        tk.Button(
            dir_frame,
            text="浏览",
            command=browse_download_dir,
            font=("Microsoft YaHei", 9)
        ).pack(side=tk.RIGHT)
        
        # 设置默认下载目录
        default_dir = f"Everything下载_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        download_dir_var.set(default_dir)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        def cancel():
            dialog.destroy()
        
        def confirm():
            search_path = search_path_var.get().strip()
            download_dir = download_dir_var.get().strip()
            strict_search = strict_search_var.get()
            
            messagebox.showinfo("配置结果", 
                               f"搜索路径: {search_path or '(全局搜索)'}\n"
                               f"下载目录: {download_dir}\n"
                               f"严格搜索: {'是' if strict_search else '否'}")
            dialog.destroy()
        
        tk.Button(
            button_frame,
            text="取消",
            command=cancel,
            font=("Microsoft YaHei", 10),
            width=10
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        tk.Button(
            button_frame,
            text="开始下载",
            command=confirm,
            font=("Microsoft YaHei", 10, "bold"),
            bg='#43A047',
            fg='white',
            width=10
        ).pack(side=tk.RIGHT)
    
    # 创建测试按钮
    test_button = tk.Button(
        root,
        text="打开Everything配置对话框",
        command=show_config,
        font=("Microsoft YaHei", 12),
        bg='#43A047',
        fg='white',
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    # 说明文本
    info_label = tk.Label(
        root,
        text="点击按钮查看配置对话框\n应该包含搜索路径输入框",
        font=("Microsoft YaHei", 10),
        justify=tk.CENTER
    )
    info_label.pack(expand=True, padx=10, pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    create_config_dialog()
