import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog, ttk
import requests
import os
import re
import json
import threading
import pyperclip
import io
from PIL import Image, ImageTk
import logging
import hashlib  # 添加hashlib库用于MD5哈希
from urllib.parse import quote  # 添加URL编码支持

class EnhancedSkuComparisonWindow:
    def __init__(self, parent, api_skus, shared_skus, cookie, headers, api_sku_search_url, api_referer):
        self.parent = parent
        self.api_skus = api_skus
        self.shared_skus = shared_skus
        self.cookie = cookie
        self.headers = headers
        self.api_sku_search_url = api_sku_search_url
        self.api_referer = api_referer
        
        self.window = tk.Toplevel(parent)
        self.window.title("增强版SKU对比")
        self.window.geometry("900x650")
        self.window.minsize(800, 600)
        self.window.configure(bg='white')
        
        self.temp_dir = "temp_images"
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
            
        self.loaded_images = {}  # 缓存已加载的图片
        self.tree_images = {}    # 保存树控件中使用的图片引用
        self.preview_window = None
        self.loading_thread = None
        self.search_var = tk.StringVar()  # 搜索变量
        self.image_mapping = {}  # SKU到图片文件路径的映射
        self.status_label = None  # 状态标签，将在create_widgets中创建
        
        # 创建默认占位图
        self.create_placeholder_image()
        
        # 居中显示窗口
        self.center_window()
        
        # 创建界面控件
        self.create_widgets()
        
        # 现在status_label已创建，可以安全地调用scan_image_directory
        self.scan_image_directory()  # 扫描图片目录
        
        self.load_data()
        
    def create_placeholder_image(self):
        """创建默认占位图像"""
        try:
            # 创建一个50x50的灰色图像作为占位图
            placeholder_small = Image.new('RGB', (50, 50), color='#DDDDDD')
            
            # 创建一个300x300的灰色图像作为预览占位图
            placeholder_preview = Image.new('RGB', (300, 300), color='#DDDDDD')
            
            # 存储占位图
            self.placeholder_images = {
                'small': ImageTk.PhotoImage(placeholder_small),
                'preview': ImageTk.PhotoImage(placeholder_preview)
            }
            
            print("已创建默认占位图")
        except Exception as e:
            print(f"创建占位图失败: {str(e)}")
            # 创建空字典，但程序仍可以运行，只是没有占位图
            self.placeholder_images = {}
        
    def create_widgets(self):
        # 创建主框架
        self.main_frame = tk.Frame(self.window, bg='white')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建搜索框架
        search_frame = tk.Frame(self.main_frame, bg='white')
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(search_frame, text="搜索:", bg='white').pack(side=tk.LEFT, padx=(0,5))
        self.search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=(0,5))
        self.search_entry.bind("<KeyRelease>", self.filter_data)
        
        tk.Button(search_frame, text="导出SKU列表", command=self.export_sku_list).pack(side=tk.RIGHT, padx=(5, 0))
        tk.Button(search_frame, text="Everything搜索下载", command=self.everything_search_download,
                 bg='#43A047', fg='white').pack(side=tk.RIGHT, padx=(5, 0))
        
        # 创建列表框架
        list_frame = tk.Frame(self.main_frame, bg='white', relief=tk.GROOVE, bd=1)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 配置ttk样式 - 添加更多配置以确保图片显示
        style = ttk.Style()
        style.theme_use('default')  # 使用默认主题，可能更好地支持图片
        style.configure("Treeview", 
                        background="white",
                        foreground="black",
                        rowheight=70,  # 进一步增加行高，确保足够空间显示图片
                        fieldbackground="white")
                        
        # 修改选中项的样式，确保文字保持黑色
        style.map('Treeview', 
                 background=[('selected', '#E0E0FF')],
                 foreground=[('selected', 'black')])  # 选中时文字保持黑色
                 
        style.layout("Treeview", [('Treeview.treearea', {'sticky': 'nswe'})])  # 重要：确保treearea正确布局
        
        # 创建Treeview控件 - 使用tree和image参数
        # 注意：show参数设置为tree和headings，确保图标列显示
        self.tree = ttk.Treeview(
            list_frame, 
            columns=("sku", "name", "status"),  # 移除image列，使用内置的图标列
            show="tree headings",  # 显示树和列标题
            selectmode="browse",
            style="Treeview"
        )
        
        # 设置列标题 - 不再需要为图片设置标题
        self.tree.heading("#0", text="图片")  # 使用内置图标列作为图片列
        self.tree.heading("sku", text="SKU")
        self.tree.heading("name", text="商品名称")
        self.tree.heading("status", text="状态")
        
        # 设置列宽
        self.tree.column("#0", width=70, anchor="center", stretch=False)  # 增加图标列宽度
        self.tree.column("sku", width=150)
        self.tree.column("name", width=450)
        self.tree.column("status", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局树形控件和滚动条
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.tree.bind("<Motion>", self.on_mouse_move)  # 鼠标移动事件
        self.tree.bind("<Button-3>", self.on_right_click)  # 右键点击事件
        
        # 创建状态栏
        self.status_label = tk.Label(self.main_frame, text="准备就绪", bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 创建控制按钮框架
        button_frame = tk.Frame(self.main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(button_frame, text="刷新数据", command=self.refresh_data).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def center_window(self):
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
    def scan_image_directory(self):
        """扫描图片目录，创建SKU到图片文件路径的映射"""
        try:
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.config(text="正在扫描图片目录...")
                self.window.update_idletasks()
            
            # 获取temp_images目录中的所有图片文件
            if not os.path.exists(self.temp_dir):
                print(f"创建图片目录: {self.temp_dir}")
                os.makedirs(self.temp_dir)
                if hasattr(self, 'status_label') and self.status_label:
                    self.status_label.config(text="已创建图片目录")
                return
                
            image_files = [f for f in os.listdir(self.temp_dir) if f.startswith("ML") and f.endswith(".png")]
            if not image_files:
                print("未找到图片文件")
                if hasattr(self, 'status_label') and self.status_label:
                    self.status_label.config(text="未找到图片文件")
                return
                
            print(f"找到 {len(image_files)} 个图片文件")
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.config(text=f"找到 {len(image_files)} 个图片文件")
            
            # 获取需要映射的SKU
            missing_skus = list(set(self.api_skus) - set(self.shared_skus))
            
            # 创建映射关系
            mapped_count = 0
            for sku in missing_skus:
                # 使用新算法生成文件名
                hash_value = hashlib.md5(sku.encode()).hexdigest()[:8]
                file_name = f"ML{hash_value}.png"
                file_path = os.path.join(self.temp_dir, file_name)
                
                # 如果文件存在，添加到映射
                if os.path.exists(file_path):
                    self.image_mapping[sku] = file_path
                    mapped_count += 1
            
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.config(text=f"完成图片映射: {mapped_count}/{len(missing_skus)}")
                
        except Exception as e:
            print(f"扫描图片目录时出错: {str(e)}")
        
    def load_data(self):
        # 获取缺失的SKU
        missing_skus = set(self.api_skus) - set(self.shared_skus)
        
        # 清空当前显示
        self.tree.delete(*self.tree.get_children())
        
        # 添加缺失的SKU
        for sku in sorted(missing_skus):
            # 使用新的列结构：(sku, name, status)
            # 注意：第二个参数是父节点ID，第三个参数是当前节点ID（留空自动生成）
            # text参数是显示在图标列的文本（留空）
            self.tree.insert("", tk.END, text="", values=(sku, "加载中...", "缺失"), tags=(sku,))
        
        # 开始加载SKU详情
        if self.loading_thread is not None and self.loading_thread.is_alive():
            return
            
        self.loading_thread = threading.Thread(target=self.load_sku_details, daemon=True)
        self.loading_thread.start()
    
    def load_sku_details(self):
        # 获取所有待加载的SKU项
        items = self.tree.get_children()
        total_items = len(items)
        
        # 逐个处理SKU
        for i, item in enumerate(items):
            try:
                current_values = self.tree.item(item)["values"]
                if len(current_values) < 2:
                    continue
                    
                sku = current_values[0]  # SKU现在是第一列
                
                # 更新状态
                self.status_label.config(text=f"正在加载第 {i+1}/{total_items} 个SKU详情: {sku}")
                self.window.update_idletasks()
                
                # 获取SKU的商品信息和图片
                product_name, image_url = self.get_sku_details(sku)
                
                if product_name:
                    # 更新商品名称和状态，注意values中现在只有三列
                    self.tree.item(item, values=(sku, product_name, "缺失"))
                    
                    # 如果有图片，异步加载图片
                    if image_url:
                        thread = threading.Thread(
                            target=lambda s=sku, u=image_url, i=item: self.load_and_cache_image(s, u, i), 
                            daemon=True
                        )
                        thread.start()
                else:
                    self.tree.item(item, values=(sku, "未找到商品信息", "缺失"))
            except Exception as e:
                try:
                    # 异常处理
                    current_values = self.tree.item(item)["values"]
                    if len(current_values) >= 1:
                        sku = current_values[0]  # SKU现在是第一列
                        self.tree.item(item, values=(sku, f"加载失败: {str(e)[:30]}", "缺失"))
                except:
                    pass
        
        # 更新加载完成状态
        self.status_label.config(text=f"所有SKU信息加载完成，共 {total_items} 个")
    
    def get_sku_details(self, sku):
        # 设置请求头
        headers = {
            'User-Agent': self.headers['User-Agent'],
            'Cookie': self.cookie,
            'Referer': self.api_referer,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # 设置请求参数
        payload = {
            'sortName': '2',
            'pageNo': '1',
            'pageSize': '50',
            'searchType': '2',
            'searchValue': sku,
            'productSearchType': '1',
            'shopId': '-1',
            'dxmState': 'online',
            'site': '0',
            'fullCid': '',
            'sortValue': '2',
            'productType': '',
            'productStatus': 'ONLINE'
        }
        
        # 发送请求获取SKU信息
        response = requests.post(self.api_sku_search_url, headers=headers, data=payload, timeout=30)
        response.raise_for_status()
        
        # 解析JSON响应
        json_data = response.json()
        
        # 检查响应是否成功
        if json_data.get('code') != 0 or 'data' not in json_data:
            return None, None
            
        # 获取商品列表
        product_list = json_data.get('data', {}).get('page', {}).get('list', [])
        if not product_list:
            return None, None
            
        # 获取第一个商品信息
        product_info = product_list[0]
        product_name = product_info.get('productName', '')
        
        # 获取商品图片URL
        thumb_url = None
        variations = product_info.get('variations', [])
        if variations and variations[0].get('thumbUrl'):
            thumb_url = variations[0].get('thumbUrl')
            
        return product_name, thumb_url
        
    def load_and_cache_image(self, sku, image_url, item_id):
        try:
            # 使用新算法生成文件路径
            hash_value = hashlib.md5(sku.encode()).hexdigest()[:8]
            new_file_name = f"ML{hash_value}.png"
            new_path = os.path.join(self.temp_dir, new_file_name)
            
            # 标记是否需要重命名
            need_rename = False
            current_path = new_path
            
            # 检查是否有预先映射的图片文件路径
            if sku in self.image_mapping:
                mapped_path = self.image_mapping[sku]
                if os.path.exists(mapped_path) and mapped_path != new_path:
                    print(f"使用映射的图片路径: {mapped_path} 用于SKU: {sku}")
                    current_path = mapped_path
                    need_rename = True  # 图片存在但路径不是标准路径，需要重命名
            
            # 如果图片不存在，下载图片
            if not os.path.exists(current_path):
                try:
                    # 设置请求头
                    api_headers = {
                        'User-Agent': self.headers['User-Agent'],
                        'Referer': self.api_referer,
                        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
                    }
                    
                    # 下载图片
                    print(f"下载SKU {sku} 的图片...")
                    img_response = requests.get(image_url, headers=api_headers, timeout=15)
                    img_response.raise_for_status()
                    
                    # 保存图片到本地，使用新算法生成的文件名
                    with open(new_path, 'wb') as f:
                        f.write(img_response.content)
                    
                    # 更新映射和当前路径
                    self.image_mapping[sku] = new_path
                    current_path = new_path
                    print(f"已下载图片并保存到: {new_path}")
                except Exception as download_err:
                    print(f"下载图片失败: {str(download_err)}")
                    # 使用占位图并更新UI
                    self.use_placeholder_image(sku, item_id)
                    return
            elif need_rename:
                # 如果需要重命名，将现有图片复制到新路径
                try:
                    import shutil
                    # 如果新路径已存在，先删除
                    if os.path.exists(new_path):
                        os.remove(new_path)
                    # 复制而不是重命名，以避免在Windows下的文件占用问题
                    shutil.copy2(current_path, new_path)
                    print(f"已将图片从 {current_path} 复制到 {new_path}")
                    # 更新路径和映射
                    self.image_mapping[sku] = new_path
                    current_path = new_path
                except Exception as e:
                    print(f"复制图片文件时出错: {str(e)}")
                    # 继续使用原始路径
            
            # 打开和处理图片
            try:
                # 检查文件是否存在
                if not os.path.exists(current_path):
                    raise FileNotFoundError(f"图片文件不存在: {current_path}")
                
                # 检查文件大小
                file_size = os.path.getsize(current_path)
                if file_size == 0:
                    raise ValueError(f"图片文件大小为0: {current_path}")
                
                # 打开图片
                img = Image.open(current_path)
                print(f"成功打开图片: {current_path}, 原始大小: {img.size}, 格式: {img.format}, 模式: {img.mode}")
                
                # 创建两种尺寸的图片
                small_img = img.resize((50, 50), Image.LANCZOS)
                preview_img = img.resize((300, 300), Image.LANCZOS)
                print(f"已调整图片大小: 小图 {small_img.size}, 预览图 {preview_img.size}")
                
                # 存储到缓存中
                try:
                    small_tk = ImageTk.PhotoImage(small_img)
                    preview_tk = ImageTk.PhotoImage(preview_img)
                    
                    print(f"已创建TK图片对象: 小图 {small_tk.width()}x{small_tk.height()}, 预览图 {preview_tk.width()}x{preview_tk.height()}")
                    
                    self.loaded_images[sku] = {
                        'small': small_tk,
                        'preview': preview_tk,
                        'path': current_path
                    }
                    
                    # 在主线程中更新UI
                    self.window.after(0, lambda: self.update_tree_image(item_id, sku))
                except Exception as tk_err:
                    print(f"创建TK图片对象失败: {str(tk_err)}")
                    raise
                
            except Exception as img_err:
                print(f"处理图片失败 {sku}: {str(img_err)}")
                # 使用占位图并更新UI
                self.use_placeholder_image(sku, item_id)
                
        except Exception as e:
            print(f"加载图片失败 {sku}: {str(e)}")
            # 使用占位图并更新UI
            self.use_placeholder_image(sku, item_id)
    
    def use_placeholder_image(self, sku, item_id):
        """当图片加载失败时使用占位图"""
        if hasattr(self, 'placeholder_images') and 'small' in self.placeholder_images:
            # 使用占位图
            self.loaded_images[sku] = {
                'small': self.placeholder_images['small'],
                'preview': self.placeholder_images['preview'],
                'path': None  # 没有实际文件路径
            }
            
            # 保存图片引用，防止被垃圾回收
            self.tree_images[sku] = self.placeholder_images['small']
            
            # 在主线程中更新UI，状态标记为"加载失败"
            self.window.after(0, lambda: self.update_tree_image_with_status(item_id, sku, "加载失败"))
            
    def update_tree_image_with_status(self, item_id, sku, status):
        """使用指定状态更新树形控件中的图片"""
        try:
            if item_id and sku in self.loaded_images:
                # 获取当前值
                current_values = self.tree.item(item_id)["values"]
                
                if len(current_values) >= 2:  # 现在只有三列：sku, name, status
                    try:
                        # 更新值，将状态设为指定值
                        self.tree.item(item_id, values=(sku, current_values[1], status), 
                                     image=self.tree_images[sku],
                                     text="")  # 设置空文本，避免显示默认文本
                        
                        print(f"为SKU {sku} 设置状态 {status} 和图片: {self.tree_images[sku].width()}x{self.tree_images[sku].height()}")
                        
                        # 在UI上强制刷新树形控件
                        self.tree.update()
                        
                    except Exception as inner_e:
                        print(f"更新树节点值和图片时出错: {str(inner_e)}")
        except Exception as e:
            print(f"更新树视图图片失败: {str(e)}")
    
    def update_tree_image(self, item_id, sku):
        try:
            if item_id and sku in self.loaded_images:
                # 获取当前值
                current_values = self.tree.item(item_id)["values"]
                
                if len(current_values) >= 2:  # 现在只有三列：sku, name, status
                    # 保存图片引用，防止被垃圾回收
                    self.tree_images[sku] = self.loaded_images[sku]['small']
                    
                    try:
                        print(f"DEBUG: 更新前item配置: {self.tree.item(item_id)}")
                        
                        # 使用分步更新，避免可能的冲突
                        # 步骤1: 先更新值
                        self.tree.item(item_id, values=(sku, current_values[1], "已加载"))
                        self.tree.update()
                        
                        # 步骤2: 设置图片和文本 - 设置为空文本，避免显示默认文本
                        self.tree.item(item_id, image=self.tree_images[sku], text="")
                        self.tree.update()
                        
                        print(f"DEBUG: 设置后检查item: {self.tree.item(item_id)}")
                        print(f"DEBUG: 图片对象ID: {id(self.tree_images[sku])}")
                        print(f"DEBUG: 图片大小: {self.tree_images[sku].width()}x{self.tree_images[sku].height()}")
                        
                        # 高亮显示有图片的行
                        self.tree.tag_configure("has_image", background="#E6FFE6")  # 淡绿色背景
                        self.tree.item(item_id, tags=("has_image",))
                        
                        # 添加一条明显的消息，确认图片已设置
                        self.status_label.config(text=f"已为SKU {sku} 设置图片 (宽度:{self.tree_images[sku].width()})")
                        
                    except Exception as inner_e:
                        print(f"更新树节点值和图片时出错: {str(inner_e)}")
        except Exception as e:
            print(f"更新树视图图片失败: {str(e)}")
        
    def on_mouse_move(self, event):
        # 获取鼠标所在的行和列
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        
        # 调试信息
        if item and column == "#0":
            print(f"DEBUG: 鼠标移动到行 {item}, 列 {column}")
        
        # 如果不在图片列上，隐藏预览窗口
        if not item or not column == "#0":  # 仅当鼠标在图标列（图片列）时显示预览
            if self.preview_window:
                self.preview_window.destroy()
                self.preview_window = None
            return
            
        # 获取行的数据
        values = self.tree.item(item)["values"]
        if not values or len(values) < 1:
            print(f"DEBUG: 行 {item} 没有足够的值: {values}")
            return
            
        # 获取SKU
        sku = values[0]  # SKU现在是第一列
        print(f"DEBUG: 获取到SKU: {sku}")
        
        # 如果该SKU没有加载图片，则不显示预览
        if sku not in self.loaded_images:
            print(f"DEBUG: SKU {sku} 没有加载图片")
            return
        else:
            print(f"DEBUG: SKU {sku} 有加载的图片: {self.loaded_images[sku]['preview'].width()}x{self.loaded_images[sku]['preview'].height()}")
            
        # 获取鼠标在屏幕上的坐标
        x, y = self.window.winfo_pointerxy()
        
        # 创建预览窗口
        self.create_preview(sku, x, y)
    
    def on_right_click(self, event):
        # 获取点击的行
        item = self.tree.identify_row(event.y)
        if not item:
            return
            
        # 获取行数据
        values = self.tree.item(item)["values"]
        if not values or len(values) < 2:
            return
            
        # 提取SKU和商品名称
        sku = values[0]  # SKU现在是第一列
        name = values[1]  # 名称现在是第二列
        
        # 创建右键菜单
        menu = tk.Menu(self.window, tearoff=0)
        
        # 只有当商品名称存在且有效时才添加复制商品名的选项
        if name and name != "加载中..." and not name.startswith("加载失败") and not name.startswith("未找到"):
            # 1. 复制完整商品名
            menu_label = f"复制商品名: {name[:30]}..." if len(name) > 30 else f"复制商品名: {name}"
            menu.add_command(label=menu_label, command=lambda: self.copy_to_clipboard(name))
            
            # 2. 复制去除前后30个字符的商品名
            if len(name) > 60:  # 只有当名称长度超过60个字符时才显示此选项
                trimmed_name = name[20:-20]
                menu_label = f"复制商品名(去除前后20字符): {trimmed_name[:20]}..."
                menu.add_command(label=menu_label, command=lambda: self.copy_to_clipboard(trimmed_name))
        
        # 3. 复制SKU
        menu.add_command(label=f"复制SKU: {sku}", command=lambda: self.copy_to_clipboard(sku))
        
        # 显示菜单
        menu.tk_popup(event.x_root, event.y_root)
    
    def copy_to_clipboard(self, text):
        # 复制文本到剪贴板
        pyperclip.copy(text)
        self.status_label.config(text=f"已复制: {text[:30]}...")
        
    def search_sku(self):
        # 获取搜索文本
        search_text = self.search_var.get().lower().strip()
        
        # 如果搜索文本为空，清除所有高亮
        if not search_text:
            for item in self.tree.get_children():
                self.tree.item(item, tags=())
            return
            
        # 遍历每一行，查找匹配项
        for item in self.tree.get_children():
            values = self.tree.item(item)["values"]
            
            # 确保有有效值
            if len(values) >= 3:
                sku = values[1].lower()  # SKU在第二列
                name = values[2].lower()  # 名称在第三列
                
                # 如果在SKU或名称中找到匹配，高亮显示
                if search_text in sku or search_text in name:
                    self.tree.item(item, tags=("match",))
                    self.tree.see(item)  # 滚动到匹配项
                else:
                    self.tree.item(item, tags=())
                    
        # 设置匹配项的高亮样式
        self.tree.tag_configure("match", background="#FFFF99")
    
    def on_search_change(self, *args):
        # 搜索文本变化时自动搜索
        self.search_sku() 

    def create_preview(self, sku, x, y):
        # 如果预览窗口已经存在，则销毁
        if self.preview_window:
            self.preview_window.destroy()
            
        # 创建预览窗口
        self.preview_window = tk.Toplevel(self.window)
        self.preview_window.overrideredirect(True)  # 无边框
        self.preview_window.configure(bg='white', bd=2, relief=tk.RAISED)
        
        # 计算预览窗口位置
        preview_x = x + 20
        preview_y = y - 150  # 将预览窗口向上移动
        
        # 确保预览窗口不超出屏幕边界
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        if preview_x + 320 > screen_width:
            preview_x = screen_width - 320
        if preview_y + 320 > screen_height:
            preview_y = screen_height - 320
        if preview_y < 0:
            preview_y = 0
            
        # 设置预览窗口位置
        self.preview_window.geometry(f"320x320+{preview_x}+{preview_y}")
        
        print(f"DEBUG: 创建预览窗口 - SKU:{sku}, 位置:{preview_x},{preview_y}")
        
        try:
            # 创建图片标签并显示图片
            if 'preview' in self.loaded_images[sku]:
                preview_image = self.loaded_images[sku]['preview']
                print(f"DEBUG: 预览图片大小: {preview_image.width()}x{preview_image.height()}")
                
                # 创建包含图片和文本的框架
                frame = tk.Frame(self.preview_window, bg='white')
                frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                
                # 图片标签
                image_label = tk.Label(frame, image=preview_image, bg='white')
                image_label.pack(padx=5, pady=5)
                
                # SKU标签
                sku_label = tk.Label(frame, text=f"SKU: {sku}", bg='white', font=("Arial", 10, "bold"))
                sku_label.pack(pady=(0, 5))
                
                # 确保图片显示
                self.preview_window.update()
        except Exception as e:
            print(f"创建预览窗口时出错: {str(e)}")

    def filter_data(self, event=None):
        """根据搜索框内容过滤树形控件中的数据"""
        search_text = self.search_var.get().strip().lower()
        
        # 如果搜索文本为空，显示所有项
        if not search_text:
            for item in self.tree.get_children():
                self.tree.item(item, open=True)  # 确保项目可见
            return
            
        # 遍历所有项目，匹配搜索文本
        for item in self.tree.get_children():
            values = self.tree.item(item)["values"]
            if len(values) >= 2:
                sku = values[0].lower()  # SKU
                name = values[1].lower()  # 商品名称
                
                # 如果SKU或商品名称包含搜索文本，显示该项，否则隐藏
                if search_text in sku or search_text in name:
                    self.tree.item(item, open=True)  # 确保项目可见
                    self.tree.selection_add(item)  # 选中匹配的项
                else:
                    self.tree.selection_remove(item)  # 取消选中不匹配的项
                    
        # 更新状态栏
        self.status_label.config(text=f"已根据 '{search_text}' 过滤结果")
        
    def export_sku_list(self):
        """导出SKU列表到文本文件"""
        # 打开文件对话框，选择保存位置
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="保存SKU列表"
        )
        
        if not file_path:
            return
            
        try:
            # 收集所有SKU
            skus = []
            for item in self.tree.get_children():
                values = self.tree.item(item)["values"]
                if len(values) >= 1:
                    skus.append(values[0])  # SKU在第一列
                    
            # 按字母顺序排序
            skus.sort()
            
            # 写入文件
            with open(file_path, "w", encoding="utf-8") as f:
                for sku in skus:
                    f.write(f"{sku}\n")
                    
            # 更新状态栏
            self.status_label.config(text=f"已导出 {len(skus)} 个SKU到文件: {os.path.basename(file_path)}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出SKU列表时出错: {str(e)}")
            
    def refresh_data(self):
        """刷新数据显示"""
        self.load_data()
        self.status_label.config(text="数据已刷新")

    def everything_search_download(self):
        """使用Everything搜索并下载商品图片"""
        try:
            # 获取所有缺失的SKU
            missing_skus = []
            for item in self.tree.get_children():
                values = self.tree.item(item)["values"]
                if len(values) >= 1:
                    missing_skus.append(values[0])  # SKU在第一列

            if not missing_skus:
                messagebox.showinfo("提示", "没有找到需要下载的SKU")
                return

            # 弹出配置对话框
            config_dialog = EverythingConfigDialog(self.window, missing_skus)
            if not config_dialog.result:
                return

            search_path = config_dialog.result.get('search_path', '')
            download_dir = config_dialog.result.get('download_dir', '')
            strict_search = config_dialog.result.get('strict_search', False)

            # 开始批量搜索下载
            self.start_everything_batch_download(missing_skus, search_path, download_dir, strict_search)

        except Exception as e:
            messagebox.showerror("错误", f"Everything搜索下载出错: {str(e)}")
            print(f"Everything搜索下载出错: {str(e)}")

    def start_everything_batch_download(self, skus, search_path, download_dir, strict_search):
        """开始批量Everything搜索下载"""
        # 在后台线程中执行下载任务
        download_thread = threading.Thread(
            target=self.everything_batch_download_worker,
            args=(skus, search_path, download_dir, strict_search),
            daemon=True
        )
        download_thread.start()

    def everything_batch_download_worker(self, skus, search_path, download_dir, strict_search):
        """Everything批量下载工作线程"""
        total_skus = len(skus)
        success_count = 0

        for i, sku in enumerate(skus):
            try:
                # 更新状态
                self.window.after(0, lambda s=sku, idx=i+1, total=total_skus:
                    self.status_label.config(text=f"正在处理 {idx}/{total}: {s}"))

                # 获取商品名称
                product_name = self.get_product_name_for_sku(sku)
                if not product_name:
                    print(f"无法获取SKU {sku} 的商品名称")
                    continue

                # 执行Everything搜索下载
                result = self.process_product_with_everything(
                    product_name, sku, download_dir, search_path, strict_search
                )

                if "成功" in result:
                    success_count += 1

            except Exception as e:
                print(f"处理SKU {sku} 时出错: {str(e)}")

        # 更新最终状态
        self.window.after(0, lambda: self.status_label.config(
            text=f"Everything下载完成: 成功 {success_count}/{total_skus}"
        ))

    def get_product_name_for_sku(self, sku):
        """获取SKU对应的商品名称"""
        try:
            # 从树形控件中查找对应的商品名称
            for item in self.tree.get_children():
                values = self.tree.item(item)["values"]
                if len(values) >= 2 and values[0] == sku:
                    product_name = values[1]
                    if product_name and product_name != "加载中..." and not product_name.startswith("加载失败"):
                        return product_name

            # 如果树形控件中没有找到，尝试通过API获取
            product_name, _ = self.get_sku_details(sku)
            return product_name

        except Exception as e:
            print(f"获取SKU {sku} 商品名称时出错: {str(e)}")
            return None

    def process_product_with_everything(self, product_name, sku, base_dir, search_path="", strict_search=False):
        """使用Everything搜索处理单个商品"""
        try:
            # 清理商品名称，移除无关前缀
            cleaned_name = product_name.replace("2D Flat ", "").strip()
            print(f"清理后的商品名称: {cleaned_name}")

            # 构建搜索查询
            search_query = cleaned_name

            # 如果指定了搜索路径并启用了严格路径搜索
            if search_path and strict_search:
                if not search_path.endswith('\\'):
                    search_path += '\\'
                # 使用Everything的path:语法限制搜索范围
                search_query = f'path:"{search_path}" {search_query}'
                print(f"使用严格路径搜索: {search_query}")

            # Everything搜索参数格式化
            search_params = {
                "search": search_query,
                "json": 1,
                "path_column": 1,
                "size_column": 1,
                "sort": "name",
                "ascending": 1
            }

            search_url = "http://localhost:8080/"

            print(f"搜索商品: {product_name}")
            print(f"发送搜索请求: {search_query}")

            try:
                response = requests.get(
                    search_url,
                    params=search_params,
                    timeout=30
                )
                response.raise_for_status()
                data = response.json()

                print(f"搜索API请求成功，状态码: {response.status_code}")

            except requests.RequestException as e:
                error_msg = f"处理商品『{product_name}』失败: API请求错误 - {str(e)}"
                print(error_msg)
                return error_msg
            except json.JSONDecodeError as e:
                error_msg = f"处理商品『{product_name}』失败: JSON解析错误 - {str(e)}"
                print(error_msg)
                return error_msg

            # 处理搜索结果
            total_results = len(data.get('results', []))
            print(f"搜索结果: 找到 {total_results} 个匹配项")

            valid_files = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")

                print(f"检查文件: {file_name} (路径: {file_path})")

                should_download = self.should_download_file(file_path, file_name)
                if should_download:
                    valid_files.append((item, file_path))
                    print(f"文件符合条件: {file_name}")
                else:
                    print(f"文件不符合条件: {file_name}")

            if not valid_files:
                return f"处理商品『{product_name}』: 未找到符合条件的图片文件"

            # 如果找到多个文件，选择第一个
            selected_item, selected_path = valid_files[0]

            # 准备下载目录
            target_dir = self.prepare_directory(base_dir)

            # 生成新文件名
            file_extension = os.path.splitext(selected_item.get('name', ''))[-1] or '.jpg'
            new_name = f"{sku}{file_extension}"
            local_path = os.path.join(target_dir, new_name)

            # 构建Everything HTTP服务器的图片访问URL
            image_url = f"http://127.0.0.1:8080/{quote(selected_path)}"

            success = self.download_image_from_everything(image_url, local_path)

            if success:
                return f"处理商品『{product_name}』成功: 已下载到 {local_path}"
            else:
                return f"处理商品『{product_name}』失败: 图片下载失败"

        except Exception as e:
            error_msg = f"处理商品『{product_name}』时出错: {str(e)}"
            print(error_msg)
            return error_msg

    def should_download_file(self, file_path, file_name):
        """判断文件是否应该下载"""
        try:
            # 检查文件扩展名
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
            file_ext = os.path.splitext(file_name.lower())[1]

            if file_ext not in valid_extensions:
                return False

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False

            # 检查文件大小（避免下载空文件或过小的文件）
            file_size = os.path.getsize(file_path)
            if file_size < 1024:  # 小于1KB的文件可能不是有效图片
                return False

            return True

        except Exception as e:
            print(f"检查文件时出错: {str(e)}")
            return False

    def prepare_directory(self, base_dir):
        """准备下载目录"""
        try:
            if not base_dir:
                # 如果没有指定目录，使用默认目录
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                base_dir = f"Everything下载_{timestamp}"

            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
                print(f"创建下载目录: {base_dir}")

            return base_dir

        except Exception as e:
            print(f"准备目录时出错: {str(e)}")
            return "Everything下载"

    def download_image_from_everything(self, url, save_path):
        """从Everything HTTP服务器下载图片文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/'
        }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                print(f"成功下载图片: {save_path}")
                return True
            return False
        except Exception as e:
            print(f"下载图片异常: {str(e)}")
            return False

class EverythingConfigDialog:
    """Everything搜索配置对话框"""
    def __init__(self, parent, sku_list):
        self.parent = parent
        self.sku_list = sku_list
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Everything搜索配置")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.configure(bg='white')

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_dialog()

        # 创建界面
        self.create_widgets()

        # 等待对话框关闭
        self.dialog.wait_window()

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """创建对话框控件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg='white', padx=20, pady=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="Everything搜索下载配置",
            font=("Microsoft YaHei", 14, "bold"),
            bg='white',
            fg='#333333'
        )
        title_label.pack(pady=(0, 15))

        # SKU信息
        info_frame = tk.LabelFrame(main_frame, text="下载信息", bg='white', padx=10, pady=10)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(
            info_frame,
            text=f"将要下载 {len(self.sku_list)} 个SKU的图片",
            bg='white',
            font=("Microsoft YaHei", 10)
        ).pack(anchor='w')

        # 搜索路径配置
        search_frame = tk.LabelFrame(main_frame, text="搜索配置", bg='white', padx=10, pady=10)
        search_frame.pack(fill=tk.X, pady=(0, 15))

        # 搜索路径
        path_frame = tk.Frame(search_frame, bg='white')
        path_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(path_frame, text="搜索路径:", bg='white', width=12, anchor='e').pack(side=tk.LEFT)
        self.search_path_var = tk.StringVar()
        self.search_path_entry = tk.Entry(
            path_frame,
            textvariable=self.search_path_var,
            font=("Microsoft YaHei", 9),
            width=35
        )
        self.search_path_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)

        tk.Button(
            path_frame,
            text="浏览",
            command=self.browse_search_path,
            font=("Microsoft YaHei", 9)
        ).pack(side=tk.RIGHT)

        # 严格搜索选项
        self.strict_search_var = tk.BooleanVar()
        strict_check = tk.Checkbutton(
            search_frame,
            text="启用严格路径搜索（仅在指定路径中搜索）",
            variable=self.strict_search_var,
            bg='white',
            font=("Microsoft YaHei", 9)
        )
        strict_check.pack(anchor='w')

        # 下载目录配置
        download_frame = tk.LabelFrame(main_frame, text="下载配置", bg='white', padx=10, pady=10)
        download_frame.pack(fill=tk.X, pady=(0, 15))

        # 下载目录
        dir_frame = tk.Frame(download_frame, bg='white')
        dir_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(dir_frame, text="下载目录:", bg='white', width=12, anchor='e').pack(side=tk.LEFT)
        self.download_dir_var = tk.StringVar()
        self.download_dir_entry = tk.Entry(
            dir_frame,
            textvariable=self.download_dir_var,
            font=("Microsoft YaHei", 9),
            width=35
        )
        self.download_dir_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)

        tk.Button(
            dir_frame,
            text="浏览",
            command=self.browse_download_dir,
            font=("Microsoft YaHei", 9)
        ).pack(side=tk.RIGHT)

        # 设置默认下载目录
        from datetime import datetime
        default_dir = f"Everything下载_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.download_dir_var.set(default_dir)

        # 按钮框架
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=(15, 0))

        tk.Button(
            button_frame,
            text="取消",
            command=self.cancel,
            font=("Microsoft YaHei", 10),
            width=10
        ).pack(side=tk.RIGHT, padx=(5, 0))

        tk.Button(
            button_frame,
            text="开始下载",
            command=self.confirm,
            font=("Microsoft YaHei", 10, "bold"),
            bg='#43A047',
            fg='white',
            width=10
        ).pack(side=tk.RIGHT)

    def browse_search_path(self):
        """浏览搜索路径"""
        path = filedialog.askdirectory(title="选择搜索路径")
        if path:
            self.search_path_var.set(path)

    def browse_download_dir(self):
        """浏览下载目录"""
        path = filedialog.askdirectory(title="选择下载目录")
        if path:
            self.download_dir_var.set(path)

    def confirm(self):
        """确认配置"""
        search_path = self.search_path_var.get().strip()
        download_dir = self.download_dir_var.get().strip()
        strict_search = self.strict_search_var.get()

        if not download_dir:
            messagebox.showwarning("警告", "请指定下载目录")
            return

        self.result = {
            'search_path': search_path,
            'download_dir': download_dir,
            'strict_search': strict_search
        }

        self.dialog.destroy()

    def cancel(self):
        """取消配置"""
        self.result = None
        self.dialog.destroy()

def show_enhanced_sku_comparison(root, api_skus, shared_skus, cookie, headers, api_sku_search_url, api_referer):
    return EnhancedSkuComparisonWindow(
        root,
        api_skus,
        shared_skus,
        cookie,
        headers,
        api_sku_search_url,
        api_referer
    )