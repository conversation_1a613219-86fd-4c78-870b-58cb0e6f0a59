import PyInstaller.__main__
import os
import sys

# 指定图标路径
icon_path = r"G:\代码处理工具\Balloon.ico"

# 确保图标文件存在
if not os.path.exists(icon_path):
    print(f"警告: 图标文件 {icon_path} 不存在!")
    exit(1)

print(f"使用图标: {icon_path}")

# 根据操作系统确定分隔符
separator = ';' if sys.platform.startswith('win') else ':'

# 设置PyInstaller参数
PyInstaller.__main__.run([
    '商品数据提取工具_精简版.py',                # 主脚本
    '--name=商品数据提取工具',                  # 输出文件名
    '--onefile',                           # 单文件模式
    f'--icon={icon_path}',                 # 图标
    '--noconsole',                         # 无控制台窗口
    '--clean',                             # 清理临时文件
    f'--add-data=key.vdf{separator}.',     # 添加key.vdf文件
    f'--add-data=settings.ini{separator}.', # 添加settings.ini文件
    '--hidden-import=PIL._tkinter_finder', # 添加隐藏导入
])

print("打包完成!")